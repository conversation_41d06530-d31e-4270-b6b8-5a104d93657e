/**
 * صفحة التقارير
 * Reports Page
 */

class ReportsPage {
    constructor() {
        this.container = document.getElementById('reports-page');
    }

    async init() {
        this.render();
    }

    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>التقارير</h2>
            </div>
            <div class="coming-soon">
                <i class="fas fa-chart-bar fa-3x"></i>
                <h3>قريباً</h3>
                <p>سيتم إضافة صفحة التقارير قريباً</p>
            </div>
        `;
    }

    async refresh() {
        this.render();
    }
}

const reportsPage = new ReportsPage();
window.ReportsPage = ReportsPage;
window.reportsPage = reportsPage;
