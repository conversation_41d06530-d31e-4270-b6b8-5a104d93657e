/**
 * صفحة التقارير
 * Reports Page
 */

class ReportsPage {
    constructor() {
        this.container = document.getElementById('reports-page');
        this.currentReport = null;
        this.reportData = null;
        this.filters = {
            dateFrom: null,
            dateTo: null,
            companyId: null,
            courierId: null
        };
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            this.setDefaultFilters();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة التقارير:', error);
            toastManager.show('حدث خطأ في تحميل صفحة التقارير', 'error');
        }
    }

    /**
     * تعيين الفلاتر الافتراضية
     * Set default filters
     */
    setDefaultFilters() {
        const now = new Date();
        this.filters.dateFrom = new Date(now.getFullYear(), now.getMonth(), 1);
        this.filters.dateTo = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>التقارير</h2>
                <div class="page-actions">
                    <button class="action-btn secondary" onclick="reportsPage.showFiltersModal()">
                        <i class="fas fa-filter"></i>
                        فلترة
                    </button>
                    ${this.currentReport ? `
                        <button class="action-btn info" onclick="reportsPage.exportCurrentReport()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <button class="action-btn warning" onclick="reportsPage.printCurrentReport()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    ` : ''}
                </div>
            </div>

            <!-- أنواع التقارير -->
            <div class="reports-grid">
                <div class="report-card" onclick="reportsPage.generateReport('daily_summary')">
                    <div class="report-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="report-info">
                        <h3>التقرير اليومي</h3>
                        <p>ملخص شامل لأنشطة اليوم</p>
                    </div>
                </div>

                <div class="report-card" onclick="reportsPage.generateReport('courier_performance')">
                    <div class="report-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="report-info">
                        <h3>أداء المندوبين</h3>
                        <p>تقرير مفصل عن أداء كل مندوب</p>
                    </div>
                </div>

                <div class="report-card" onclick="reportsPage.generateReport('company_analysis')">
                    <div class="report-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="report-info">
                        <h3>تحليل الشركات</h3>
                        <p>تحليل شامل لأداء الشركات</p>
                    </div>
                </div>

                <div class="report-card" onclick="reportsPage.generateReport('financial_summary')">
                    <div class="report-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="report-info">
                        <h3>الملخص المالي</h3>
                        <p>تقرير الأرباح والخسائر</p>
                    </div>
                </div>

                <div class="report-card" onclick="reportsPage.generateReport('returns_analysis')">
                    <div class="report-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <div class="report-info">
                        <h3>تحليل الرواجع</h3>
                        <p>تقرير مفصل عن الرواجع</p>
                    </div>
                </div>

                <div class="report-card" onclick="reportsPage.generateReport('orders_detailed')">
                    <div class="report-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="report-info">
                        <h3>تقرير الطلبات المفصل</h3>
                        <p>قائمة شاملة بجميع الطلبات</p>
                    </div>
                </div>
            </div>

            <!-- منطقة عرض التقرير -->
            <div class="report-display" id="report-display" style="display: none;">
                <div class="report-header">
                    <h3 id="report-title">عنوان التقرير</h3>
                    <div class="report-meta">
                        <span id="report-date"></span>
                        <span id="report-period"></span>
                    </div>
                </div>

                <div class="report-content" id="report-content">
                    <!-- محتوى التقرير سيتم إدراجه هنا -->
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // لا توجد أحداث إضافية حالياً
    }

    /**
     * إنشاء تقرير
     * Generate report
     */
    async generateReport(reportType) {
        try {
            toastManager.show('جاري إنشاء التقرير...', 'info');

            let reportData;
            switch (reportType) {
                case 'daily_summary':
                    reportData = await reportsManager.generateDailySummaryReport();
                    break;
                case 'courier_performance':
                    reportData = await reportsManager.generateCourierPerformanceReport(
                        this.filters.dateFrom, this.filters.dateTo
                    );
                    break;
                case 'company_analysis':
                    reportData = await reportsManager.generateCompanyAnalysisReport(
                        this.filters.dateFrom, this.filters.dateTo
                    );
                    break;
                case 'financial_summary':
                    reportData = await reportsManager.generateFinancialSummaryReport(
                        this.filters.dateFrom, this.filters.dateTo
                    );
                    break;
                case 'returns_analysis':
                    reportData = await reportsManager.generateReturnsAnalysisReport(
                        this.filters.dateFrom, this.filters.dateTo
                    );
                    break;
                case 'orders_detailed':
                    reportData = await reportsManager.generateDetailedOrdersReport({
                        dateFrom: this.filters.dateFrom,
                        dateTo: this.filters.dateTo,
                        companyId: this.filters.companyId,
                        courierId: this.filters.courierId
                    });
                    break;
                default:
                    throw new Error('نوع تقرير غير مدعوم');
            }

            this.currentReport = reportType;
            this.reportData = reportData;
            this.displayReport(reportData);

            toastManager.show('تم إنشاء التقرير بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير:', error);
            toastManager.show('حدث خطأ في إنشاء التقرير', 'error');
        }
    }

    /**
     * عرض التقرير
     * Display report
     */
    displayReport(reportData) {
        const reportDisplay = document.getElementById('report-display');
        const reportTitle = document.getElementById('report-title');
        const reportDate = document.getElementById('report-date');
        const reportPeriod = document.getElementById('report-period');
        const reportContent = document.getElementById('report-content');

        // تحديث معلومات التقرير
        reportTitle.textContent = reportData.title;
        reportDate.textContent = `تاريخ الإنشاء: ${utils.formatDate(reportData.generatedAt, 'DD/MM/YYYY HH:mm')}`;

        if (reportData.dateFrom && reportData.dateTo) {
            reportPeriod.textContent = `الفترة: ${utils.formatDate(reportData.dateFrom, 'DD/MM/YYYY')} - ${utils.formatDate(reportData.dateTo, 'DD/MM/YYYY')}`;
        } else if (reportData.date) {
            reportPeriod.textContent = `التاريخ: ${utils.formatDate(reportData.date, 'DD/MM/YYYY')}`;
        } else {
            reportPeriod.textContent = '';
        }

        // عرض محتوى التقرير
        reportContent.innerHTML = this.renderReportContent(reportData);

        // إظهار منطقة التقرير
        reportDisplay.style.display = 'block';
        reportDisplay.scrollIntoView({ behavior: 'smooth' });

        // تحديث أزرار الصفحة
        this.render();
    }

    /**
     * رسم محتوى التقرير
     * Render report content
     */
    renderReportContent(reportData) {
        switch (reportData.type) {
            case 'daily_summary':
                return this.renderDailySummaryReport(reportData);
            case 'courier_performance':
                return this.renderCourierPerformanceReport(reportData);
            case 'company_analysis':
                return this.renderCompanyAnalysisReport(reportData);
            case 'financial_summary':
                return this.renderFinancialSummaryReport(reportData);
            case 'returns_analysis':
                return this.renderReturnsAnalysisReport(reportData);
            case 'orders_detailed':
                return this.renderOrdersDetailedReport(reportData);
            default:
                return '<p>نوع تقرير غير مدعوم</p>';
        }
    }

    /**
     * رسم التقرير اليومي
     * Render daily summary report
     */
    renderDailySummaryReport(reportData) {
        return `
            <div class="report-section">
                <h4>الملخص العام</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الطلبات</span>
                        <span class="summary-value">${reportData.summary.totalOrders}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">طلبات مسلمة</span>
                        <span class="summary-value">${reportData.summary.deliveredOrders}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">طلبات معلقة</span>
                        <span class="summary-value">${reportData.summary.pendingOrders}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">طلبات راجعة</span>
                        <span class="summary-value">${reportData.summary.returnedOrders}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الإيرادات</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.summary.totalRevenue)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي العمولات</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.summary.totalCommissions)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">صافي الربح</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.summary.totalProfit)}</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>أداء المندوبين</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي الإيرادات</th>
                                <th>العمولة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${reportData.courierStats.map(stat => `
                                <tr>
                                    <td>${stat.courier ? stat.courier.name : 'غير محدد'}</td>
                                    <td>${stat.orders}</td>
                                    <td>${utils.formatCurrency(stat.revenue)}</td>
                                    <td>${utils.formatCurrency(stat.commission)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * رسم تقرير أداء المندوبين
     * Render courier performance report
     */
    renderCourierPerformanceReport(reportData) {
        return `
            <div class="report-section">
                <h4>ملخص الأداء</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">عدد المندوبين</span>
                        <span class="summary-value">${reportData.totalCouriers}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">متوسط معدل النجاح</span>
                        <span class="summary-value">${reportData.averageSuccessRate.toFixed(1)}%</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>تفاصيل المندوبين</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>إجمالي الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>طلبات راجعة</th>
                                <th>معدل النجاح</th>
                                <th>معدل الرواجع</th>
                                <th>إجمالي العمولة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${reportData.couriers.map(courier => `
                                <tr>
                                    <td>
                                        <div class="courier-info">
                                            <strong>${courier.courier.name}</strong>
                                            <small>${courier.courier.region}</small>
                                        </div>
                                    </td>
                                    <td>${courier.totalOrders}</td>
                                    <td>${courier.deliveredOrders}</td>
                                    <td>${courier.returnedOrders}</td>
                                    <td>
                                        <span class="percentage ${courier.successRate > 80 ? 'good' : courier.successRate > 60 ? 'average' : 'low'}">
                                            ${courier.successRate.toFixed(1)}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="percentage ${courier.returnRate < 10 ? 'good' : courier.returnRate < 20 ? 'average' : 'high'}">
                                            ${courier.returnRate.toFixed(1)}%
                                        </span>
                                    </td>
                                    <td>${utils.formatCurrency(courier.totalCommission)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * عرض نافذة الفلاتر
     * Show filters modal
     */
    async showFiltersModal() {
        try {
            const companies = await db.getAll('companies', c => c.isActive);
            const couriers = await db.getAll('couriers', c => c.isActive);

            const modalContent = `
                <form id="reports-filters-form">
                    <div class="form-group">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="dateFrom" class="form-input"
                               value="${this.filters.dateFrom ? utils.formatDate(this.filters.dateFrom, 'YYYY-MM-DD') : ''}">
                    </div>

                    <div class="form-group">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="dateTo" class="form-input"
                               value="${this.filters.dateTo ? utils.formatDate(this.filters.dateTo, 'YYYY-MM-DD') : ''}">
                    </div>

                    <div class="form-group">
                        <label class="form-label">الشركة</label>
                        <select name="companyId" class="form-select">
                            <option value="">جميع الشركات</option>
                            ${companies.map(company =>
                                `<option value="${company.id}" ${this.filters.companyId == company.id ? 'selected' : ''}>
                                    ${company.name}
                                </option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">المندوب</label>
                        <select name="courierId" class="form-select">
                            <option value="">جميع المندوبين</option>
                            ${couriers.map(courier =>
                                `<option value="${courier.id}" ${this.filters.courierId == courier.id ? 'selected' : ''}>
                                    ${courier.name}
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                </form>
            `;

            const footer = `
                <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
                <button class="action-btn primary" onclick="reportsPage.applyFilters()">تطبيق</button>
            `;

            modalManager.show('فلترة التقارير', modalContent, { footer });

        } catch (error) {
            console.error('خطأ في عرض نافذة الفلاتر:', error);
            toastManager.show('حدث خطأ في عرض الفلاتر', 'error');
        }
    }

    /**
     * تطبيق الفلاتر
     * Apply filters
     */
    applyFilters() {
        try {
            const form = document.getElementById('reports-filters-form');
            const formData = new FormData(form);

            this.filters.dateFrom = formData.get('dateFrom') ? new Date(formData.get('dateFrom')) : null;
            this.filters.dateTo = formData.get('dateTo') ? new Date(formData.get('dateTo')) : null;
            this.filters.companyId = formData.get('companyId') ? parseInt(formData.get('companyId')) : null;
            this.filters.courierId = formData.get('courierId') ? parseInt(formData.get('courierId')) : null;

            modalManager.hide();
            toastManager.show('تم تطبيق الفلاتر بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تطبيق الفلاتر:', error);
            toastManager.show('حدث خطأ في تطبيق الفلاتر', 'error');
        }
    }

    /**
     * تصدير التقرير الحالي
     * Export current report
     */
    exportCurrentReport() {
        if (!this.reportData) {
            toastManager.show('لا يوجد تقرير لتصديره', 'warning');
            return;
        }

        try {
            reportsManager.exportToExcel(this.reportData);
        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            toastManager.show('حدث خطأ في تصدير التقرير', 'error');
        }
    }

    /**
     * طباعة التقرير الحالي
     * Print current report
     */
    printCurrentReport() {
        if (!this.reportData) {
            toastManager.show('لا يوجد تقرير للطباعة', 'warning');
            return;
        }

        try {
            reportsManager.printReport(this.reportData);
        } catch (error) {
            console.error('خطأ في طباعة التقرير:', error);
            toastManager.show('حدث خطأ في طباعة التقرير', 'error');
        }
    }

    /**
     * رسم تقرير تحليل الشركات
     * Render company analysis report
     */
    renderCompanyAnalysisReport(reportData) {
        return `
            <div class="report-section">
                <h4>ملخص الشركات</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">عدد الشركات</span>
                        <span class="summary-value">${reportData.totalCompanies}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الإيرادات</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.totalRevenue)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الأرباح</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.totalProfit)}</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>تفاصيل الشركات</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الشركة</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي الإيرادات</th>
                                <th>إجمالي الأرباح</th>
                                <th>هامش الربح</th>
                                <th>متوسط قيمة الطلب</th>
                                <th>معدل الرواجع</th>
                                <th>رضا العملاء</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${reportData.companies.map(company => `
                                <tr>
                                    <td>
                                        <div class="company-info">
                                            <strong>${company.company.name}</strong>
                                            <small>${company.company.phone || ''}</small>
                                        </div>
                                    </td>
                                    <td>${company.totalOrders}</td>
                                    <td>${utils.formatCurrency(company.totalRevenue)}</td>
                                    <td>${utils.formatCurrency(company.totalProfit)}</td>
                                    <td>
                                        <span class="percentage ${company.profitMargin > 20 ? 'good' : company.profitMargin > 10 ? 'average' : 'low'}">
                                            ${company.profitMargin.toFixed(1)}%
                                        </span>
                                    </td>
                                    <td>${utils.formatCurrency(company.averageOrderValue)}</td>
                                    <td>
                                        <span class="percentage ${company.returnRate < 5 ? 'good' : company.returnRate < 10 ? 'average' : 'high'}">
                                            ${company.returnRate.toFixed(1)}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="percentage ${company.customerSatisfaction > 90 ? 'good' : company.customerSatisfaction > 80 ? 'average' : 'low'}">
                                            ${company.customerSatisfaction.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * رسم الملخص المالي
     * Render financial summary report
     */
    renderFinancialSummaryReport(reportData) {
        return `
            <div class="report-section">
                <h4>الإيرادات</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الإيرادات</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.revenue.totalRevenue)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">متوسط قيمة الطلب</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.revenue.averageOrderValue)}</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>التكاليف</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي العمولات</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.costs.totalCommissions)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">التكاليف التشغيلية</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.costs.operatingCosts)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">نسبة التكاليف</span>
                        <span class="summary-value">${reportData.costs.costRatio.toFixed(1)}%</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>الأرباح</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الربح</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.profit.grossProfit)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">صافي الربح</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.profit.netProfit)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">هامش الربح</span>
                        <span class="summary-value">${reportData.profit.profitMargin.toFixed(1)}%</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>الأداء العام</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الطلبات</span>
                        <span class="summary-value">${reportData.performance.totalOrders}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الرواجع</span>
                        <span class="summary-value">${reportData.performance.totalReturns}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">معدل الرواجع</span>
                        <span class="summary-value">${reportData.performance.returnRate.toFixed(1)}%</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">المندوبين النشطين</span>
                        <span class="summary-value">${reportData.performance.activeCouriers}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الشركات النشطة</span>
                        <span class="summary-value">${reportData.performance.activeCompanies}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * رسم تحليل الرواجع
     * Render returns analysis report
     */
    renderReturnsAnalysisReport(reportData) {
        return `
            <div class="report-section">
                <h4>ملخص الرواجع</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الرواجع</span>
                        <span class="summary-value">${reportData.summary.totalReturns}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">رواجع مستلمة</span>
                        <span class="summary-value">${reportData.summary.receivedReturns}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">رواجع معلقة</span>
                        <span class="summary-value">${reportData.summary.pendingReturns}</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>الرواجع حسب النوع</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>نوع الراجع</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(reportData.summary.returnsByType).map(([type, count]) => `
                                <tr>
                                    <td>${type}</td>
                                    <td>${count}</td>
                                    <td>${((count / reportData.summary.totalReturns) * 100).toFixed(1)}%</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="report-section">
                <h4>الرواجع حسب المندوب</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>عدد الرواجع</th>
                                <th>إجمالي المبلغ</th>
                                <th>الأنواع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${reportData.courierAnalysis.map(analysis => `
                                <tr>
                                    <td>${analysis.courier ? analysis.courier.name : 'غير محدد'}</td>
                                    <td>${analysis.totalReturns}</td>
                                    <td>${utils.formatCurrency(analysis.totalAmount)}</td>
                                    <td>
                                        ${Object.entries(analysis.returnsByType).map(([type, count]) =>
                                            `<span class="return-type-tag">${type}: ${count}</span>`
                                        ).join(' ')}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * رسم تقرير الطلبات المفصل
     * Render detailed orders report
     */
    renderOrdersDetailedReport(reportData) {
        return `
            <div class="report-section">
                <h4>ملخص الطلبات</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الطلبات</span>
                        <span class="summary-value">${reportData.summary.totalOrders}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المبلغ</span>
                        <span class="summary-value">${utils.formatCurrency(reportData.summary.totalAmount)}</span>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h4>الطلبات حسب الحالة</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(reportData.summary.statusCounts).map(([status, count]) => `
                                <tr>
                                    <td>${this.getStatusText(status)}</td>
                                    <td>${count}</td>
                                    <td>${((count / reportData.summary.totalOrders) * 100).toFixed(1)}%</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="report-section">
                <h4>تفاصيل الطلبات</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الوصل</th>
                                <th>الشركة</th>
                                <th>اسم الزبون</th>
                                <th>المبلغ</th>
                                <th>المندوب</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${reportData.orders.slice(0, 100).map(order => `
                                <tr>
                                    <td>${order.receiptNumber}</td>
                                    <td>${order.companyName}</td>
                                    <td>${order.customerName}</td>
                                    <td>${order.formattedAmount}</td>
                                    <td>${order.courierName}</td>
                                    <td>
                                        <span class="status-badge ${order.status}">
                                            ${order.statusText}
                                        </span>
                                    </td>
                                    <td>${order.formattedDate}</td>
                                </tr>
                            `).join('')}
                            ${reportData.orders.length > 100 ?
                                `<tr><td colspan="7" class="text-center">
                                    <em>عرض أول 100 طلب من إجمالي ${reportData.orders.length} طلب</em>
                                </td></tr>` : ''
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * الحصول على نص الحالة
     * Get status text
     */
    getStatusText(status) {
        const statusTexts = {
            'delivered': 'مُسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي',
            'delayed': 'مؤجل'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        this.render();
    }
}

const reportsPage = new ReportsPage();
window.ReportsPage = ReportsPage;
window.reportsPage = reportsPage;
