/**
 * صفحة إدارة المندوبين
 * Couriers Management Page
 */

class CouriersPage {
    constructor() {
        this.container = document.getElementById('couriers-page');
        this.tableManager = null;
        this.couriers = [];
        this.regions = [];
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة المندوبين:', error);
            toastManager.show('حدث خطأ في تحميل صفحة المندوبين', 'error');
        }
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            this.couriers = await db.getAll('couriers');
            this.regions = await db.getAll('regions');
        } catch (error) {
            console.error('خطأ في تحميل بيانات المندوبين:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>إدارة المندوبين</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="couriersPage.showAddCourierModal()">
                        <i class="fas fa-plus"></i>
                        مندوب جديد
                    </button>
                    <button class="action-btn secondary" onclick="couriersPage.showRegionsModal()">
                        <i class="fas fa-map-marker-alt"></i>
                        إدارة المناطق
                    </button>
                    <button class="action-btn info" onclick="couriersPage.exportCouriers()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-couriers">${this.couriers.length}</h3>
                        <p>إجمالي المندوبين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-couriers">${this.couriers.filter(c => c.isActive).length}</h3>
                        <p>مندوبين نشطين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="inactive-couriers">${this.couriers.filter(c => !c.isActive).length}</h3>
                        <p>مندوبين غير نشطين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-regions">${this.regions.length}</h3>
                        <p>عدد المناطق</p>
                    </div>
                </div>
            </div>

            <div class="table-container" id="couriers-table-container">
                <!-- سيتم إدراج الجدول هنا -->
            </div>
        `;

        this.renderTable();
    }

    /**
     * رسم الجدول
     * Render table
     */
    renderTable() {
        const tableContainer = document.getElementById('couriers-table-container');
        if (!tableContainer) return;

        // إعداد أعمدة الجدول
        const columns = [
            { key: 'name', title: 'اسم المندوب' },
            { key: 'phone', title: 'رقم الهاتف', render: (value) => utils.formatPhone(value) },
            { key: 'region', title: 'المنطقة' },
            { key: 'commission', title: 'العمولة %', render: (value) => `${value || 0}%` },
            { key: 'totalOrders', title: 'إجمالي الطلبات', render: (value) => value || 0 },
            { key: 'completedOrders', title: 'طلبات مكتملة', render: (value) => value || 0 },
            { key: 'successRate', title: 'معدل النجاح %', render: (value) => `${(value || 0).toFixed(1)}%` },
            { key: 'totalCommission', title: 'إجمالي العمولة', render: (value) => utils.formatCurrency(value || 0) },
            { key: 'isActive', title: 'الحالة', render: (value) => this.renderStatusBadge(value) },
            { key: 'actions', title: 'الإجراءات', render: (value, row) => this.renderActions(row) }
        ];

        // إنشاء مدير الجدول
        this.tableManager = new TableManager('couriers-table-container', {
            columns: columns,
            searchable: true,
            sortable: true,
            paginated: true,
            pageSize: 15
        });

        this.tableManager.setData(this.couriers);
    }

    /**
     * رسم شارة الحالة
     * Render status badge
     */
    renderStatusBadge(isActive) {
        if (isActive) {
            return '<span class="badge badge-success">نشط</span>';
        } else {
            return '<span class="badge badge-danger">غير نشط</span>';
        }
    }

    /**
     * رسم أزرار الإجراءات
     * Render action buttons
     */
    renderActions(courier) {
        return `
            <div class="action-buttons">
                <button class="btn-sm btn-primary" onclick="couriersPage.viewCourier(${courier.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-sm btn-secondary" onclick="couriersPage.editCourier(${courier.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-sm btn-info" onclick="couriersPage.viewOrders(${courier.id})" title="الطلبات">
                    <i class="fas fa-box"></i>
                </button>
                <button class="btn-sm btn-warning" onclick="couriersPage.toggleStatus(${courier.id})" title="تغيير الحالة">
                    <i class="fas fa-toggle-on"></i>
                </button>
                <button class="btn-sm btn-danger" onclick="couriersPage.deleteCourier(${courier.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // لا توجد أحداث إضافية حالياً
    }

    /**
     * عرض نافذة إضافة مندوب جديد
     * Show add courier modal
     */
    showAddCourierModal() {
        const regionsOptions = this.regions.map(region => 
            `<option value="${region.name}">${region.name}</option>`
        ).join('');

        const modalContent = `
            <form id="add-courier-form">
                <div class="form-group">
                    <label class="form-label">اسم المندوب *</label>
                    <input type="text" name="name" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">رقم الهاتف *</label>
                    <input type="tel" name="phone" class="form-input" required placeholder="05xxxxxxxx">
                </div>
                
                <div class="form-group">
                    <label class="form-label">المنطقة *</label>
                    <select name="region" class="form-select" required>
                        <option value="">اختر المنطقة</option>
                        ${regionsOptions}
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">العمولة % *</label>
                    <input type="number" name="commission" class="form-input" step="0.1" min="0" max="100" required value="5">
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" class="form-textarea" rows="3"></textarea>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="couriersPage.saveCourier()">حفظ المندوب</button>
        `;

        modalManager.show('مندوب جديد', modalContent, { footer, maxWidth: '500px' });
    }

    /**
     * حفظ مندوب جديد
     * Save new courier
     */
    async saveCourier() {
        try {
            const form = document.getElementById('add-courier-form');
            const formData = new FormData(form);
            
            const courierData = {
                name: formData.get('name').trim(),
                phone: formData.get('phone').trim(),
                region: formData.get('region'),
                commission: parseFloat(formData.get('commission')),
                notes: formData.get('notes').trim()
            };

            // التحقق من صحة البيانات
            if (!courierData.name || courierData.name.length < 2) {
                toastManager.show('اسم المندوب مطلوب ويجب أن يكون أكثر من حرفين', 'warning');
                return;
            }

            if (!utils.validatePhone(courierData.phone)) {
                toastManager.show('رقم الهاتف غير صحيح', 'warning');
                return;
            }

            if (!courierData.region) {
                toastManager.show('المنطقة مطلوبة', 'warning');
                return;
            }

            if (isNaN(courierData.commission) || courierData.commission < 0) {
                toastManager.show('العمولة يجب أن تكون رقم موجب', 'warning');
                return;
            }

            // حفظ المندوب
            await dataManager.addCourier(courierData);
            
            modalManager.hide();
            toastManager.show('تم إضافة المندوب بنجاح', 'success');
            
            // تحديث الصفحة
            await this.refresh();

        } catch (error) {
            console.error('خطأ في حفظ المندوب:', error);
            toastManager.show(error.message || 'حدث خطأ في حفظ المندوب', 'error');
        }
    }

    /**
     * عرض تفاصيل المندوب
     * View courier details
     */
    async viewCourier(courierId) {
        try {
            const courier = await db.getById('couriers', courierId);
            if (!courier) {
                toastManager.show('لم يتم العثور على المندوب', 'error');
                return;
            }

            // الحصول على طلبات المندوب
            const orders = await db.getByIndex('orders', 'courierId', courierId);
            const commissions = await accountingManager.calculateCourierCommissions(courierId);

            const modalContent = `
                <div class="courier-details">
                    <div class="detail-section">
                        <h4>المعلومات الأساسية</h4>
                        <div class="detail-row">
                            <strong>الاسم:</strong>
                            <span>${courier.name}</span>
                        </div>
                        <div class="detail-row">
                            <strong>رقم الهاتف:</strong>
                            <span>${utils.formatPhone(courier.phone)}</span>
                        </div>
                        <div class="detail-row">
                            <strong>المنطقة:</strong>
                            <span>${courier.region}</span>
                        </div>
                        <div class="detail-row">
                            <strong>العمولة:</strong>
                            <span>${courier.commission}%</span>
                        </div>
                        <div class="detail-row">
                            <strong>الحالة:</strong>
                            <span class="badge badge-${courier.isActive ? 'success' : 'danger'}">
                                ${courier.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>الإحصائيات</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">إجمالي الطلبات</span>
                                <span class="stat-value">${orders.length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">طلبات مكتملة</span>
                                <span class="stat-value">${orders.filter(o => o.status === 'delivered').length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">معدل النجاح</span>
                                <span class="stat-value">${courier.successRate ? courier.successRate.toFixed(1) : 0}%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">إجمالي العمولة</span>
                                <span class="stat-value">${utils.formatCurrency(commissions.totalCommission)}</span>
                            </div>
                        </div>
                    </div>

                    ${courier.notes ? `
                        <div class="detail-section">
                            <h4>ملاحظات</h4>
                            <p>${courier.notes}</p>
                        </div>
                    ` : ''}
                </div>
            `;

            const footer = `
                <button class="action-btn secondary" onclick="modalManager.hide()">إغلاق</button>
                <button class="action-btn primary" onclick="modalManager.hide(); couriersPage.editCourier(${courierId})">تعديل</button>
            `;

            modalManager.show('تفاصيل المندوب', modalContent, { footer, maxWidth: '600px' });

        } catch (error) {
            console.error('خطأ في عرض تفاصيل المندوب:', error);
            toastManager.show('حدث خطأ في عرض تفاصيل المندوب', 'error');
        }
    }

    /**
     * تعديل المندوب
     * Edit courier
     */
    editCourier(courierId) {
        // سيتم تنفيذها لاحقاً
        toastManager.show('سيتم إضافة تعديل المندوبين قريباً', 'info');
    }

    /**
     * عرض طلبات المندوب
     * View courier orders
     */
    viewOrders(courierId) {
        // الانتقال إلى صفحة الطلبات مع فلتر المندوب
        navigateToPage('orders');
        // سيتم إضافة الفلتر لاحقاً
    }

    /**
     * تغيير حالة المندوب
     * Toggle courier status
     */
    async toggleStatus(courierId) {
        try {
            const courier = await db.getById('couriers', courierId);
            if (!courier) {
                toastManager.show('لم يتم العثور على المندوب', 'error');
                return;
            }

            await db.update('couriers', {
                ...courier,
                isActive: !courier.isActive,
                updatedDate: new Date()
            });

            toastManager.show(`تم ${courier.isActive ? 'إلغاء تفعيل' : 'تفعيل'} المندوب بنجاح`, 'success');
            await this.refresh();

        } catch (error) {
            console.error('خطأ في تغيير حالة المندوب:', error);
            toastManager.show('حدث خطأ في تغيير حالة المندوب', 'error');
        }
    }

    /**
     * حذف المندوب
     * Delete courier
     */
    deleteCourier(courierId) {
        modalManager.confirm(
            'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا المندوب؟ سيتم إلغاء إسناد جميع طلباته.',
            async () => {
                try {
                    // إلغاء إسناد الطلبات
                    const orders = await db.getByIndex('orders', 'courierId', courierId);
                    for (const order of orders) {
                        await db.update('orders', {
                            ...order,
                            courierId: null,
                            updatedDate: new Date()
                        });
                    }

                    // حذف المندوب
                    await db.delete('couriers', courierId);
                    
                    toastManager.show('تم حذف المندوب بنجاح', 'success');
                    await this.refresh();

                } catch (error) {
                    console.error('خطأ في حذف المندوب:', error);
                    toastManager.show('حدث خطأ في حذف المندوب', 'error');
                }
            }
        );
    }

    /**
     * عرض نافذة إدارة المناطق
     * Show regions management modal
     */
    showRegionsModal() {
        // سيتم تنفيذها لاحقاً
        toastManager.show('سيتم إضافة إدارة المناطق قريباً', 'info');
    }

    /**
     * تصدير المندوبين
     * Export couriers
     */
    exportCouriers() {
        try {
            const exportData = this.couriers.map(courier => ({
                'اسم المندوب': courier.name,
                'رقم الهاتف': courier.phone,
                'المنطقة': courier.region,
                'العمولة %': courier.commission,
                'إجمالي الطلبات': courier.totalOrders || 0,
                'طلبات مكتملة': courier.completedOrders || 0,
                'معدل النجاح %': (courier.successRate || 0).toFixed(1),
                'إجمالي العمولة': courier.totalCommission || 0,
                'الحالة': courier.isActive ? 'نشط' : 'غير نشط',
                'تاريخ الإضافة': utils.formatDate(courier.createdDate, 'DD/MM/YYYY'),
                'ملاحظات': courier.notes || ''
            }));

            utils.exportToCSV(exportData, `couriers_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
            toastManager.show('تم تصدير المندوبين بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير المندوبين:', error);
            toastManager.show('حدث خطأ في تصدير المندوبين', 'error');
        }
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadData();
        this.render();
    }
}

// إنشاء مثيل الصفحة
const couriersPage = new CouriersPage();

// تصدير الصفحة
window.CouriersPage = CouriersPage;
window.couriersPage = couriersPage;
