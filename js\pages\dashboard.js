/**
 * صفحة لوحة التحكم
 * Dashboard Page
 */

class DashboardPage {
    constructor() {
        this.container = document.getElementById('dashboard-page');
        this.stats = {
            totalOrders: 0,
            deliveredOrders: 0,
            pendingOrders: 0,
            returnedOrders: 0
        };
        this.recentOrders = [];
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة لوحة التحكم:', error);
            toastManager.show('حدث خطأ في تحميل لوحة التحكم', 'error');
        }
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            // تحميل جميع الطلبات
            const allOrders = await db.getAll('orders', order => !order.isArchived);
            
            // حساب الإحصائيات
            this.stats.totalOrders = allOrders.length;
            this.stats.deliveredOrders = allOrders.filter(order => order.status === 'delivered').length;
            this.stats.pendingOrders = allOrders.filter(order => order.status === 'pending').length;
            this.stats.returnedOrders = allOrders.filter(order => order.status === 'returned').length;

            // الحصول على آخر 10 طلبات
            this.recentOrders = allOrders
                .sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate))
                .slice(0, 10);

        } catch (error) {
            console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        // تحديث بطاقات الإحصائيات
        this.updateStatsCards();
        
        // تحديث الطلبات الأخيرة
        this.updateRecentOrders();
    }

    /**
     * تحديث بطاقات الإحصائيات
     * Update stats cards
     */
    updateStatsCards() {
        const totalOrdersElement = document.getElementById('total-orders');
        const deliveredOrdersElement = document.getElementById('delivered-orders');
        const pendingOrdersElement = document.getElementById('pending-orders');
        const returnedOrdersElement = document.getElementById('returned-orders');

        if (totalOrdersElement) {
            totalOrdersElement.textContent = this.stats.totalOrders;
        }

        if (deliveredOrdersElement) {
            deliveredOrdersElement.textContent = this.stats.deliveredOrders;
        }

        if (pendingOrdersElement) {
            pendingOrdersElement.textContent = this.stats.pendingOrders;
        }

        if (returnedOrdersElement) {
            returnedOrdersElement.textContent = this.stats.returnedOrders;
        }
    }

    /**
     * تحديث الطلبات الأخيرة
     * Update recent orders
     */
    async updateRecentOrders() {
        const recentOrdersContainer = document.getElementById('recent-orders');
        
        if (!recentOrdersContainer) return;

        if (this.recentOrders.length === 0) {
            recentOrdersContainer.innerHTML = '<p class="no-data">لا توجد طلبات حديثة</p>';
            return;
        }

        let html = '';
        
        for (const order of this.recentOrders) {
            // الحصول على اسم الشركة
            let companyName = 'غير محدد';
            if (order.companyId) {
                try {
                    const company = await db.getById('companies', order.companyId);
                    if (company) {
                        companyName = company.name;
                    }
                } catch (error) {
                    console.error('خطأ في الحصول على اسم الشركة:', error);
                }
            }

            // الحصول على اسم المندوب
            let courierName = 'غير مُسند';
            if (order.courierId) {
                try {
                    const courier = await db.getById('couriers', order.courierId);
                    if (courier) {
                        courierName = courier.name;
                    }
                } catch (error) {
                    console.error('خطأ في الحصول على اسم المندوب:', error);
                }
            }

            const statusClass = this.getStatusClass(order.status);
            const statusText = this.getStatusText(order.status);

            html += `
                <div class="order-item" onclick="viewOrderDetails(${order.id})" style="cursor: pointer;"
                    <div class="order-info">
                        <div class="order-number">وصل رقم: ${order.receiptNumber}</div>
                        <div class="order-details">
                            ${companyName} • ${order.customerName} • ${courierName}
                            <br>
                            <small>${utils.formatDate(order.createdDate, 'relative')}</small>
                        </div>
                    </div>
                    <div class="order-status ${statusClass}">
                        ${statusText}
                    </div>
                </div>
            `;
        }

        recentOrdersContainer.innerHTML = html;
    }

    /**
     * الحصول على فئة حالة الطلب
     * Get order status class
     */
    getStatusClass(status) {
        const statusClasses = {
            'delivered': 'delivered',
            'pending': 'pending',
            'returned': 'returned',
            'partial_return': 'returned',
            'delayed': 'pending'
        };
        return statusClasses[status] || 'pending';
    }

    /**
     * الحصول على نص حالة الطلب
     * Get order status text
     */
    getStatusText(status) {
        const statusTexts = {
            'delivered': 'مُسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي',
            'delayed': 'مؤجل'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // تحديث البيانات كل 30 ثانية
        this.refreshInterval = setInterval(() => {
            this.refresh();
        }, 30000);
    }

    /**
     * تحديث البيانات
     * Refresh data
     */
    async refresh() {
        try {
            await this.loadData();
            this.render();
        } catch (error) {
            console.error('خطأ في تحديث لوحة التحكم:', error);
        }
    }

    /**
     * عرض تفاصيل الطلب
     * View order details
     */
    async viewOrderDetails(orderId) {
        try {
            const order = await db.getById('orders', orderId);
            if (!order) {
                toastManager.show('لم يتم العثور على الطلب', 'error');
                return;
            }

            // الحصول على تفاصيل إضافية
            let companyName = 'غير محدد';
            let courierName = 'غير مُسند';

            if (order.companyId) {
                const company = await db.getById('companies', order.companyId);
                if (company) companyName = company.name;
            }

            if (order.courierId) {
                const courier = await db.getById('couriers', order.courierId);
                if (courier) courierName = courier.name;
            }

            const modalContent = `
                <div class="order-details-modal">
                    <div class="detail-row">
                        <strong>رقم الوصل:</strong>
                        <span>${order.receiptNumber}</span>
                    </div>
                    <div class="detail-row">
                        <strong>الشركة:</strong>
                        <span>${companyName}</span>
                    </div>
                    <div class="detail-row">
                        <strong>اسم الزبون:</strong>
                        <span>${order.customerName}</span>
                    </div>
                    <div class="detail-row">
                        <strong>هاتف الزبون:</strong>
                        <span>${utils.formatPhone(order.customerPhone)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>العنوان:</strong>
                        <span>${order.address}</span>
                    </div>
                    <div class="detail-row">
                        <strong>المبلغ:</strong>
                        <span>${utils.formatCurrency(order.amount)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>المندوب:</strong>
                        <span>${courierName}</span>
                    </div>
                    <div class="detail-row">
                        <strong>الحالة:</strong>
                        <span class="order-status ${this.getStatusClass(order.status)}">
                            ${this.getStatusText(order.status)}
                        </span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ الإنشاء:</strong>
                        <span>${utils.formatDate(order.createdDate, 'DD/MM/YYYY HH:mm')}</span>
                    </div>
                    ${order.notes ? `
                        <div class="detail-row">
                            <strong>ملاحظات:</strong>
                            <span>${order.notes}</span>
                        </div>
                    ` : ''}
                </div>
            `;

            const footer = `
                <button class="action-btn secondary" onclick="modalManager.hide()">إغلاق</button>
                <button class="action-btn primary" onclick="navigateToPage('orders')">إدارة الطلبات</button>
            `;

            modalManager.show('تفاصيل الطلب', modalContent, { footer });

        } catch (error) {
            console.error('خطأ في عرض تفاصيل الطلب:', error);
            toastManager.show('حدث خطأ في عرض تفاصيل الطلب', 'error');
        }
    }

    /**
     * تنظيف الصفحة
     * Cleanup page
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

/**
 * عرض نافذة إضافة طلب جديد
 * Show add new order modal
 */
async function showAddOrderModal() {
    try {
        // تحميل الشركات والمندوبين
        const companies = await db.getAll('companies', company => company.isActive);
        const couriers = await db.getAll('couriers', courier => courier.isActive);

        let companiesOptions = '<option value="">اختر الشركة</option>';
        companies.forEach(company => {
            companiesOptions += `<option value="${company.id}">${company.name}</option>`;
        });

        let couriersOptions = '<option value="">اختر المندوب (اختياري)</option>';
        couriers.forEach(courier => {
            couriersOptions += `<option value="${courier.id}">${courier.name}</option>`;
        });

        const modalContent = `
            <form id="add-order-form">
                <div class="form-group">
                    <label class="form-label">رقم الوصل</label>
                    <input type="text" name="receiptNumber" class="form-input" value="${utils.generateReceiptNumber()}" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الشركة</label>
                    <select name="companyId" class="form-select" required>
                        ${companiesOptions}
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">اسم الزبون</label>
                    <input type="text" name="customerName" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">هاتف الزبون</label>
                    <input type="tel" name="customerPhone" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">العنوان</label>
                    <textarea name="address" class="form-textarea" required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">المبلغ</label>
                    <input type="number" name="amount" class="form-input" step="0.01" min="0" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">المندوب</label>
                    <select name="courierId" class="form-select">
                        ${couriersOptions}
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" class="form-textarea"></textarea>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="saveNewOrder()">حفظ الطلب</button>
        `;

        modalManager.show('طلب جديد', modalContent, { footer, maxWidth: '600px' });

    } catch (error) {
        console.error('خطأ في عرض نافذة الطلب الجديد:', error);
        toastManager.show('حدث خطأ في عرض النافذة', 'error');
    }
}

/**
 * حفظ طلب جديد
 * Save new order
 */
async function saveNewOrder() {
    try {
        const form = document.getElementById('add-order-form');
        const formData = new FormData(form);
        
        const orderData = {
            receiptNumber: formData.get('receiptNumber'),
            companyId: parseInt(formData.get('companyId')),
            customerName: formData.get('customerName'),
            customerPhone: formData.get('customerPhone'),
            address: formData.get('address'),
            amount: parseFloat(formData.get('amount')),
            courierId: formData.get('courierId') ? parseInt(formData.get('courierId')) : null,
            notes: formData.get('notes'),
            status: 'pending',
            createdDate: new Date(),
            isArchived: false
        };

        // التحقق من صحة البيانات
        if (!orderData.receiptNumber || !orderData.companyId || !orderData.customerName || 
            !orderData.customerPhone || !orderData.address || !orderData.amount) {
            toastManager.show('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الوصل
        const existingOrder = await db.getByIndex('orders', 'receiptNumber', orderData.receiptNumber);
        if (existingOrder.length > 0) {
            toastManager.show('رقم الوصل موجود مسبقاً', 'error');
            return;
        }

        // حفظ الطلب
        await db.add('orders', orderData);
        
        modalManager.hide();
        toastManager.show('تم إضافة الطلب بنجاح', 'success');
        
        // تحديث لوحة التحكم
        if (window.app.pages.dashboard) {
            await window.app.pages.dashboard.refresh();
        }

    } catch (error) {
        console.error('خطأ في حفظ الطلب:', error);
        toastManager.show('حدث خطأ في حفظ الطلب', 'error');
    }
}

// تصدير الصفحة
window.DashboardPage = DashboardPage;
window.showAddOrderModal = showAddOrderModal;
window.saveNewOrder = saveNewOrder;

// تصدير دالة عرض تفاصيل الطلب للاستخدام العام
window.viewOrderDetails = async function(orderId) {
    if (window.app && window.app.pages && window.app.pages.dashboard) {
        await window.app.pages.dashboard.viewOrderDetails(orderId);
    } else {
        // إنشاء مثيل مؤقت إذا لم تكن الصفحة محملة
        const tempDashboard = new DashboardPage();
        await tempDashboard.viewOrderDetails(orderId);
    }
};
