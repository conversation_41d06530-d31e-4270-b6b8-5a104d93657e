/* ===== CSS Variables ===== */
:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Neumorphic Colors */
    --bg-primary: #e6e7ee;
    --bg-secondary: #f0f0f3;
    --shadow-light: #ffffff;
    --shadow-dark: #d1d9e6;
    
    /* Typography */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Neumorphic Shadows */
    --neu-shadow-inset: inset 5px 5px 10px var(--shadow-dark), inset -5px -5px 10px var(--shadow-light);
    --neu-shadow-outset: 5px 5px 10px var(--shadow-dark), -5px -5px 10px var(--shadow-light);
    --neu-shadow-pressed: inset 3px 3px 6px var(--shadow-dark), inset -3px -3px 6px var(--shadow-light);
    
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===== Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background: var(--bg-primary);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* ===== Loading Screen ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--primary-color);
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: var(--spacing-4);
    animation: bounce 1s infinite;
}

.loading-spinner p {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== App Container ===== */
.app-container {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* ===== Sidebar ===== */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    box-shadow: var(--neu-shadow-outset);
    border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transition: all var(--transition-normal);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: var(--spacing-6) var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    color: var(--primary-color);
}

.logo i {
    font-size: var(--font-size-2xl);
}

.logo h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    transition: opacity var(--transition-normal);
}

.sidebar.collapsed .logo h2 {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

/* ===== Sidebar Menu ===== */
.sidebar-menu {
    list-style: none;
    padding: var(--spacing-4) 0;
}

.menu-item {
    margin: var(--spacing-1) var(--spacing-4);
}

.menu-item a {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    position: relative;
}

.menu-item a:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    box-shadow: var(--neu-shadow-inset);
}

.menu-item.active a {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: var(--neu-shadow-pressed);
}

.menu-item a i {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.menu-item a span {
    font-weight: 500;
    transition: opacity var(--transition-normal);
}

.sidebar.collapsed .menu-item a span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* ===== Main Content ===== */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    transition: margin-right var(--transition-normal);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed + .main-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* ===== Header ===== */
.header {
    height: var(--header-height);
    background: var(--bg-secondary);
    box-shadow: var(--neu-shadow-outset);
    border-radius: var(--radius-xl) 0 0 var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-6);
    margin: var(--spacing-4) 0 var(--spacing-4) var(--spacing-4);
    position: sticky;
    top: var(--spacing-4);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

#page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
}

/* ===== Header Stats ===== */
.header-stats {
    display: flex;
    gap: var(--spacing-6);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-bottom: var(--spacing-1);
}

.stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== User Menu ===== */
.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: none;
    border: none;
    color: var(--gray-700);
    font-size: var(--font-size-base);
    cursor: pointer;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.user-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.user-btn i:first-child {
    font-size: var(--font-size-xl);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.user-dropdown a:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.user-dropdown a:first-child {
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.user-dropdown a:last-child {
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}
