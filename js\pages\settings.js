/**
 * صفحة الإعدادات
 * Settings Page
 */

class SettingsPage {
    constructor() {
        this.container = document.getElementById('settings-page');
        this.currentSection = 'general';
        this.settings = {};
        this.regions = [];
        this.users = [];
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadSettings();
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة الإعدادات:', error);
            toastManager.show('حدث خطأ في تحميل صفحة الإعدادات', 'error');
        }
    }

    /**
     * تحميل الإعدادات
     * Load settings
     */
    async loadSettings() {
        try {
            const settingsData = await db.getAll('settings');
            this.settings = {};

            // تحويل الإعدادات إلى كائن
            settingsData.forEach(setting => {
                this.settings[setting.key] = setting.value;
            });

            // إعدادات افتراضية
            this.setDefaultSettings();
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            this.setDefaultSettings();
        }
    }

    /**
     * تعيين الإعدادات الافتراضية
     * Set default settings
     */
    setDefaultSettings() {
        const defaults = {
            companyName: 'شركة التوصيل العراقية',
            companyPhone: '',
            companyEmail: '',
            companyAddress: '',
            defaultCommission: 5,
            currency: 'دينار',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24',
            language: 'ar',
            theme: 'light',
            autoArchiveDays: 90,
            receiptPrefix: 'R',
            enableNotifications: true,
            enableSounds: true,
            backupFrequency: 'weekly'
        };

        Object.keys(defaults).forEach(key => {
            if (this.settings[key] === undefined) {
                this.settings[key] = defaults[key];
            }
        });
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            this.regions = await db.getAll('regions');
            this.users = await db.getAll('users');
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>الإعدادات</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="settingsPage.saveAllSettings()">
                        <i class="fas fa-save"></i>
                        حفظ جميع الإعدادات
                    </button>
                    <button class="action-btn secondary" onclick="settingsPage.resetToDefaults()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                    <button class="action-btn info" onclick="settingsPage.exportSettings()">
                        <i class="fas fa-download"></i>
                        تصدير الإعدادات
                    </button>
                </div>
            </div>

            <!-- قائمة الإعدادات الجانبية -->
            <div class="settings-layout">
                <div class="settings-sidebar">
                    <div class="settings-nav">
                        <button class="settings-nav-item ${this.currentSection === 'general' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('general')">
                            <i class="fas fa-cog"></i>
                            إعدادات عامة
                        </button>
                        <button class="settings-nav-item ${this.currentSection === 'company' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('company')">
                            <i class="fas fa-building"></i>
                            معلومات الشركة
                        </button>
                        <button class="settings-nav-item ${this.currentSection === 'orders' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('orders')">
                            <i class="fas fa-box"></i>
                            إعدادات الطلبات
                        </button>
                        <button class="settings-nav-item ${this.currentSection === 'regions' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('regions')">
                            <i class="fas fa-map-marker-alt"></i>
                            إدارة المناطق
                        </button>
                        <button class="settings-nav-item ${this.currentSection === 'users' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('users')">
                            <i class="fas fa-users"></i>
                            إدارة المستخدمين
                        </button>
                        <button class="settings-nav-item ${this.currentSection === 'backup' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('backup')">
                            <i class="fas fa-database"></i>
                            النسخ الاحتياطي
                        </button>
                        <button class="settings-nav-item ${this.currentSection === 'advanced' ? 'active' : ''}"
                                onclick="settingsPage.switchSection('advanced')">
                            <i class="fas fa-tools"></i>
                            إعدادات متقدمة
                        </button>
                    </div>
                </div>

                <div class="settings-content">
                    <div id="settings-content-area">
                        ${this.renderCurrentSection()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * رسم القسم الحالي
     * Render current section
     */
    renderCurrentSection() {
        switch (this.currentSection) {
            case 'general':
                return this.renderGeneralSettings();
            case 'company':
                return this.renderCompanySettings();
            case 'orders':
                return this.renderOrdersSettings();
            case 'regions':
                return this.renderRegionsSettings();
            case 'users':
                return this.renderUsersSettings();
            case 'backup':
                return this.renderBackupSettings();
            case 'advanced':
                return this.renderAdvancedSettings();
            default:
                return this.renderGeneralSettings();
        }
    }

    /**
     * رسم الإعدادات العامة
     * Render general settings
     */
    renderGeneralSettings() {
        return `
            <div class="settings-section">
                <h3>الإعدادات العامة</h3>

                <form id="general-settings-form">
                    <div class="form-group">
                        <label class="form-label">اللغة</label>
                        <select name="language" class="form-select">
                            <option value="ar" ${this.settings.language === 'ar' ? 'selected' : ''}>العربية</option>
                            <option value="en" ${this.settings.language === 'en' ? 'selected' : ''}>English</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">المظهر</label>
                        <select name="theme" class="form-select">
                            <option value="light" ${this.settings.theme === 'light' ? 'selected' : ''}>فاتح</option>
                            <option value="dark" ${this.settings.theme === 'dark' ? 'selected' : ''}>داكن</option>
                            <option value="auto" ${this.settings.theme === 'auto' ? 'selected' : ''}>تلقائي</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">العملة</label>
                        <select name="currency" class="form-select">
                            <option value="دينار" ${this.settings.currency === 'دينار' ? 'selected' : ''}>دينار عراقي</option>
                            <option value="ريال" ${this.settings.currency === 'ريال' ? 'selected' : ''}>ريال سعودي</option>
                            <option value="درهم" ${this.settings.currency === 'درهم' ? 'selected' : ''}>درهم إماراتي</option>
                            <option value="دولار" ${this.settings.currency === 'دولار' ? 'selected' : ''}>دولار أمريكي</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">تنسيق التاريخ</label>
                        <select name="dateFormat" class="form-select">
                            <option value="DD/MM/YYYY" ${this.settings.dateFormat === 'DD/MM/YYYY' ? 'selected' : ''}>DD/MM/YYYY</option>
                            <option value="MM/DD/YYYY" ${this.settings.dateFormat === 'MM/DD/YYYY' ? 'selected' : ''}>MM/DD/YYYY</option>
                            <option value="YYYY-MM-DD" ${this.settings.dateFormat === 'YYYY-MM-DD' ? 'selected' : ''}>YYYY-MM-DD</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">تنسيق الوقت</label>
                        <select name="timeFormat" class="form-select">
                            <option value="12" ${this.settings.timeFormat === '12' ? 'selected' : ''}>12 ساعة</option>
                            <option value="24" ${this.settings.timeFormat === '24' ? 'selected' : ''}>24 ساعة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="enableNotifications" ${this.settings.enableNotifications ? 'checked' : ''}>
                            <span>تفعيل الإشعارات</span>
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="enableSounds" ${this.settings.enableSounds ? 'checked' : ''}>
                            <span>تفعيل الأصوات</span>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="action-btn primary" onclick="settingsPage.saveGeneralSettings()">
                            حفظ الإعدادات العامة
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * رسم إعدادات الشركة
     * Render company settings
     */
    renderCompanySettings() {
        return `
            <div class="settings-section">
                <h3>معلومات الشركة</h3>

                <form id="company-settings-form">
                    <div class="form-group">
                        <label class="form-label">اسم الشركة *</label>
                        <input type="text" name="companyName" class="form-input"
                               value="${this.settings.companyName || ''}" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" name="companyPhone" class="form-input"
                               value="${this.settings.companyPhone || ''}" placeholder="07xxxxxxxx">
                    </div>

                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="companyEmail" class="form-input"
                               value="${this.settings.companyEmail || ''}" placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea name="companyAddress" class="form-textarea" rows="3">${this.settings.companyAddress || ''}</textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">شعار الشركة</label>
                        <input type="file" name="companyLogo" class="form-input" accept="image/*">
                        <small class="form-help">يُستخدم في التقارير والفواتير</small>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="action-btn primary" onclick="settingsPage.saveCompanySettings()">
                            حفظ معلومات الشركة
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * رسم إعدادات الطلبات
     * Render orders settings
     */
    renderOrdersSettings() {
        return `
            <div class="settings-section">
                <h3>إعدادات الطلبات</h3>

                <form id="orders-settings-form">
                    <div class="form-group">
                        <label class="form-label">العمولة الافتراضية للمندوبين (%)</label>
                        <input type="number" name="defaultCommission" class="form-input"
                               value="${this.settings.defaultCommission || 5}" min="0" max="100" step="0.1">
                    </div>

                    <div class="form-group">
                        <label class="form-label">بادئة رقم الوصل</label>
                        <input type="text" name="receiptPrefix" class="form-input"
                               value="${this.settings.receiptPrefix || 'R'}" maxlength="5">
                        <small class="form-help">مثال: R12345</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">أرشفة الطلبات تلقائياً بعد (يوم)</label>
                        <select name="autoArchiveDays" class="form-select">
                            <option value="30" ${this.settings.autoArchiveDays == 30 ? 'selected' : ''}>30 يوم</option>
                            <option value="60" ${this.settings.autoArchiveDays == 60 ? 'selected' : ''}>60 يوم</option>
                            <option value="90" ${this.settings.autoArchiveDays == 90 ? 'selected' : ''}>90 يوم</option>
                            <option value="180" ${this.settings.autoArchiveDays == 180 ? 'selected' : ''}>180 يوم</option>
                            <option value="365" ${this.settings.autoArchiveDays == 365 ? 'selected' : ''}>سنة واحدة</option>
                            <option value="0" ${this.settings.autoArchiveDays == 0 ? 'selected' : ''}>إلغاء الأرشفة التلقائية</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="requireCourierForOrder" ${this.settings.requireCourierForOrder ? 'checked' : ''}>
                            <span>إجبار تحديد مندوب عند إنشاء الطلب</span>
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="allowOrderEdit" ${this.settings.allowOrderEdit !== false ? 'checked' : ''}>
                            <span>السماح بتعديل الطلبات</span>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="action-btn primary" onclick="settingsPage.saveOrdersSettings()">
                            حفظ إعدادات الطلبات
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * رسم إعدادات المناطق
     * Render regions settings
     */
    renderRegionsSettings() {
        return `
            <div class="settings-section">
                <h3>إدارة المناطق</h3>

                <div class="section-actions">
                    <button class="action-btn primary" onclick="settingsPage.showAddRegionModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منطقة
                    </button>
                </div>

                <div class="regions-list">
                    ${this.regions.length === 0 ?
                        '<p class="no-data">لا توجد مناطق مضافة</p>' :
                        this.regions.map(region => `
                            <div class="region-item">
                                <div class="region-info">
                                    <strong>${region.name}</strong>
                                    ${region.description ? `<small>${region.description}</small>` : ''}
                                </div>
                                <div class="region-actions">
                                    <button class="btn-sm btn-secondary" onclick="settingsPage.editRegion(${region.id})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-sm btn-danger" onclick="settingsPage.deleteRegion(${region.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')
                    }
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // لا توجد أحداث إضافية حالياً
    }

    /**
     * تبديل القسم
     * Switch section
     */
    switchSection(section) {
        this.currentSection = section;

        // تحديث التنقل
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[onclick="settingsPage.switchSection('${section}')"]`).classList.add('active');

        // تحديث المحتوى
        document.getElementById('settings-content-area').innerHTML = this.renderCurrentSection();
    }

    /**
     * حفظ الإعدادات العامة
     * Save general settings
     */
    async saveGeneralSettings() {
        try {
            const form = document.getElementById('general-settings-form');
            const formData = new FormData(form);

            const settings = {
                language: formData.get('language'),
                theme: formData.get('theme'),
                currency: formData.get('currency'),
                dateFormat: formData.get('dateFormat'),
                timeFormat: formData.get('timeFormat'),
                enableNotifications: formData.get('enableNotifications') === 'on',
                enableSounds: formData.get('enableSounds') === 'on'
            };

            await this.saveSettings(settings);
            toastManager.show('تم حفظ الإعدادات العامة بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في حفظ الإعدادات العامة:', error);
            toastManager.show('حدث خطأ في حفظ الإعدادات', 'error');
        }
    }

    /**
     * حفظ إعدادات الشركة
     * Save company settings
     */
    async saveCompanySettings() {
        try {
            const form = document.getElementById('company-settings-form');
            const formData = new FormData(form);

            const settings = {
                companyName: formData.get('companyName'),
                companyPhone: formData.get('companyPhone'),
                companyEmail: formData.get('companyEmail'),
                companyAddress: formData.get('companyAddress')
            };

            await this.saveSettings(settings);
            toastManager.show('تم حفظ معلومات الشركة بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في حفظ معلومات الشركة:', error);
            toastManager.show('حدث خطأ في حفظ معلومات الشركة', 'error');
        }
    }

    /**
     * حفظ إعدادات الطلبات
     * Save orders settings
     */
    async saveOrdersSettings() {
        try {
            const form = document.getElementById('orders-settings-form');
            const formData = new FormData(form);

            const settings = {
                defaultCommission: parseFloat(formData.get('defaultCommission')),
                receiptPrefix: formData.get('receiptPrefix'),
                autoArchiveDays: parseInt(formData.get('autoArchiveDays')),
                requireCourierForOrder: formData.get('requireCourierForOrder') === 'on',
                allowOrderEdit: formData.get('allowOrderEdit') === 'on'
            };

            await this.saveSettings(settings);
            toastManager.show('تم حفظ إعدادات الطلبات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في حفظ إعدادات الطلبات:', error);
            toastManager.show('حدث خطأ في حفظ إعدادات الطلبات', 'error');
        }
    }

    /**
     * حفظ الإعدادات
     * Save settings
     */
    async saveSettings(newSettings) {
        try {
            for (const [key, value] of Object.entries(newSettings)) {
                this.settings[key] = value;

                // حفظ في قاعدة البيانات
                const existingSetting = await db.getByIndex('settings', 'key', key);
                if (existingSetting.length > 0) {
                    await db.update('settings', {
                        ...existingSetting[0],
                        value: value,
                        updatedDate: new Date()
                    });
                } else {
                    await db.add('settings', {
                        key: key,
                        value: value,
                        createdDate: new Date()
                    });
                }
            }
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            throw error;
        }
    }

    /**
     * رسم إعدادات المستخدمين
     * Render users settings
     */
    renderUsersSettings() {
        return `
            <div class="settings-section">
                <h3>إدارة المستخدمين</h3>

                <div class="section-actions">
                    <button class="action-btn primary" onclick="settingsPage.showAddUserModal()">
                        <i class="fas fa-plus"></i>
                        إضافة مستخدم
                    </button>
                </div>

                <div class="users-list">
                    ${this.users.length === 0 ?
                        '<p class="no-data">لا توجد مستخدمين</p>' :
                        this.users.map(user => `
                            <div class="user-item">
                                <div class="user-info">
                                    <strong>${user.name}</strong>
                                    <small>${user.username} - ${user.role}</small>
                                </div>
                                <div class="user-status">
                                    <span class="badge badge-${user.isActive ? 'success' : 'danger'}">
                                        ${user.isActive ? 'نشط' : 'غير نشط'}
                                    </span>
                                </div>
                                <div class="user-actions">
                                    <button class="btn-sm btn-secondary" onclick="settingsPage.editUser(${user.id})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-sm btn-warning" onclick="settingsPage.toggleUserStatus(${user.id})" title="تغيير الحالة">
                                        <i class="fas fa-toggle-on"></i>
                                    </button>
                                    <button class="btn-sm btn-danger" onclick="settingsPage.deleteUser(${user.id})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')
                    }
                </div>
            </div>
        `;
    }

    /**
     * رسم إعدادات النسخ الاحتياطي
     * Render backup settings
     */
    renderBackupSettings() {
        return `
            <div class="settings-section">
                <h3>النسخ الاحتياطي</h3>

                <form id="backup-settings-form">
                    <div class="form-group">
                        <label class="form-label">تكرار النسخ الاحتياطي</label>
                        <select name="backupFrequency" class="form-select">
                            <option value="daily" ${this.settings.backupFrequency === 'daily' ? 'selected' : ''}>يومي</option>
                            <option value="weekly" ${this.settings.backupFrequency === 'weekly' ? 'selected' : ''}>أسبوعي</option>
                            <option value="monthly" ${this.settings.backupFrequency === 'monthly' ? 'selected' : ''}>شهري</option>
                            <option value="manual" ${this.settings.backupFrequency === 'manual' ? 'selected' : ''}>يدوي فقط</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="autoBackup" ${this.settings.autoBackup ? 'checked' : ''}>
                            <span>تفعيل النسخ الاحتياطي التلقائي</span>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="action-btn primary" onclick="settingsPage.saveBackupSettings()">
                            حفظ إعدادات النسخ الاحتياطي
                        </button>
                    </div>
                </form>

                <div class="backup-actions">
                    <h4>إجراءات النسخ الاحتياطي</h4>

                    <div class="action-buttons-grid">
                        <button class="action-btn success" onclick="settingsPage.createBackup()">
                            <i class="fas fa-download"></i>
                            إنشاء نسخة احتياطية
                        </button>

                        <button class="action-btn warning" onclick="settingsPage.showRestoreModal()">
                            <i class="fas fa-upload"></i>
                            استعادة من نسخة احتياطية
                        </button>

                        <button class="action-btn info" onclick="settingsPage.exportData()">
                            <i class="fas fa-file-export"></i>
                            تصدير البيانات
                        </button>

                        <button class="action-btn secondary" onclick="settingsPage.showImportModal()">
                            <i class="fas fa-file-import"></i>
                            استيراد البيانات
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * رسم الإعدادات المتقدمة
     * Render advanced settings
     */
    renderAdvancedSettings() {
        return `
            <div class="settings-section">
                <h3>الإعدادات المتقدمة</h3>

                <div class="warning-box">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>تحذير: هذه الإعدادات للمستخدمين المتقدمين فقط. قد تؤثر على أداء النظام.</p>
                </div>

                <form id="advanced-settings-form">
                    <div class="form-group">
                        <label class="form-label">حجم الصفحة في الجداول</label>
                        <select name="tablePageSize" class="form-select">
                            <option value="10" ${this.settings.tablePageSize == 10 ? 'selected' : ''}>10</option>
                            <option value="20" ${this.settings.tablePageSize == 20 ? 'selected' : ''}>20</option>
                            <option value="50" ${this.settings.tablePageSize == 50 ? 'selected' : ''}>50</option>
                            <option value="100" ${this.settings.tablePageSize == 100 ? 'selected' : ''}>100</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">مهلة انتظار العمليات (ثانية)</label>
                        <input type="number" name="operationTimeout" class="form-input"
                               value="${this.settings.operationTimeout || 30}" min="10" max="300">
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="enableDebugMode" ${this.settings.enableDebugMode ? 'checked' : ''}>
                            <span>تفعيل وضع التطوير (Debug Mode)</span>
                        </label>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="enablePerformanceMonitoring" ${this.settings.enablePerformanceMonitoring ? 'checked' : ''}>
                            <span>تفعيل مراقبة الأداء</span>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="action-btn primary" onclick="settingsPage.saveAdvancedSettings()">
                            حفظ الإعدادات المتقدمة
                        </button>
                    </div>
                </form>

                <div class="system-actions">
                    <h4>إجراءات النظام</h4>

                    <div class="action-buttons-grid">
                        <button class="action-btn warning" onclick="settingsPage.clearCache()">
                            <i class="fas fa-broom"></i>
                            مسح الذاكرة المؤقتة
                        </button>

                        <button class="action-btn danger" onclick="settingsPage.resetDatabase()">
                            <i class="fas fa-database"></i>
                            إعادة تعيين قاعدة البيانات
                        </button>

                        <button class="action-btn info" onclick="settingsPage.showSystemInfo()">
                            <i class="fas fa-info-circle"></i>
                            معلومات النظام
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * عرض نافذة إضافة منطقة
     * Show add region modal
     */
    showAddRegionModal() {
        const modalContent = `
            <form id="add-region-form">
                <div class="form-group">
                    <label class="form-label">اسم المنطقة *</label>
                    <input type="text" name="name" class="form-input" required>
                </div>

                <div class="form-group">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-textarea" rows="3"></textarea>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="settingsPage.saveRegion()">حفظ المنطقة</button>
        `;

        modalManager.show('إضافة منطقة جديدة', modalContent, { footer });
    }

    /**
     * حفظ منطقة
     * Save region
     */
    async saveRegion() {
        try {
            const form = document.getElementById('add-region-form');
            const formData = new FormData(form);

            const regionData = {
                name: formData.get('name').trim(),
                description: formData.get('description').trim(),
                isActive: true,
                createdDate: new Date()
            };

            if (!regionData.name) {
                toastManager.show('اسم المنطقة مطلوب', 'warning');
                return;
            }

            await db.add('regions', regionData);

            modalManager.hide();
            toastManager.show('تم إضافة المنطقة بنجاح', 'success');
            await this.refresh();

        } catch (error) {
            console.error('خطأ في حفظ المنطقة:', error);
            toastManager.show('حدث خطأ في حفظ المنطقة', 'error');
        }
    }

    /**
     * حذف منطقة
     * Delete region
     */
    async deleteRegion(regionId) {
        modalManager.confirm(
            'تأكيد الحذف',
            'هل أنت متأكد من حذف هذه المنطقة؟',
            async () => {
                try {
                    await db.delete('regions', regionId);
                    toastManager.show('تم حذف المنطقة بنجاح', 'success');
                    await this.refresh();
                } catch (error) {
                    console.error('خطأ في حذف المنطقة:', error);
                    toastManager.show('حدث خطأ في حذف المنطقة', 'error');
                }
            }
        );
    }

    /**
     * إنشاء نسخة احتياطية
     * Create backup
     */
    async createBackup() {
        try {
            toastManager.show('جاري إنشاء النسخة الاحتياطية...', 'info');

            // جمع جميع البيانات
            const backupData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                data: {}
            };

            const tables = ['orders', 'couriers', 'companies', 'regions', 'returns', 'accounting', 'settings', 'users'];

            for (const table of tables) {
                try {
                    backupData.data[table] = await db.getAll(table);
                } catch (error) {
                    console.warn(`تحذير: لا يمكن نسخ جدول ${table}:`, error);
                    backupData.data[table] = [];
                }
            }

            // تصدير البيانات
            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `backup_${utils.formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.json`;
            link.click();

            toastManager.show('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            toastManager.show('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
        }
    }

    /**
     * مسح الذاكرة المؤقتة
     * Clear cache
     */
    clearCache() {
        try {
            // مسح ذاكرة التخزين المؤقت
            if (window.dataManager) {
                window.dataManager.clearCache();
            }

            // مسح localStorage
            localStorage.clear();

            toastManager.show('تم مسح الذاكرة المؤقتة بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في مسح الذاكرة المؤقتة:', error);
            toastManager.show('حدث خطأ في مسح الذاكرة المؤقتة', 'error');
        }
    }

    /**
     * عرض معلومات النظام
     * Show system info
     */
    showSystemInfo() {
        const modalContent = `
            <div class="system-info">
                <div class="info-section">
                    <h4>معلومات النظام</h4>
                    <div class="info-item">
                        <span class="info-label">إصدار النظام:</span>
                        <span class="info-value">1.0.0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المتصفح:</span>
                        <span class="info-value">${navigator.userAgent}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">اللغة:</span>
                        <span class="info-value">${navigator.language}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المنطقة الزمنية:</span>
                        <span class="info-value">${Intl.DateTimeFormat().resolvedOptions().timeZone}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h4>إحصائيات قاعدة البيانات</h4>
                    <div id="database-stats">
                        جاري التحميل...
                    </div>
                </div>
            </div>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إغلاق</button>
        `;

        modalManager.show('معلومات النظام', modalContent, { footer, maxWidth: '600px' });

        // تحميل إحصائيات قاعدة البيانات
        this.loadDatabaseStats();
    }

    /**
     * تحميل إحصائيات قاعدة البيانات
     * Load database statistics
     */
    async loadDatabaseStats() {
        try {
            const tables = ['orders', 'couriers', 'companies', 'regions', 'returns', 'accounting', 'settings', 'users'];
            let statsHtml = '';

            for (const table of tables) {
                try {
                    const count = (await db.getAll(table)).length;
                    statsHtml += `
                        <div class="info-item">
                            <span class="info-label">${table}:</span>
                            <span class="info-value">${count} سجل</span>
                        </div>
                    `;
                } catch (error) {
                    statsHtml += `
                        <div class="info-item">
                            <span class="info-label">${table}:</span>
                            <span class="info-value error">خطأ</span>
                        </div>
                    `;
                }
            }

            const statsContainer = document.getElementById('database-stats');
            if (statsContainer) {
                statsContainer.innerHTML = statsHtml;
            }

        } catch (error) {
            console.error('خطأ في تحميل إحصائيات قاعدة البيانات:', error);
        }
    }

    /**
     * حفظ جميع الإعدادات
     * Save all settings
     */
    async saveAllSettings() {
        try {
            toastManager.show('جاري حفظ جميع الإعدادات...', 'info');

            // حفظ الإعدادات حسب القسم الحالي
            switch (this.currentSection) {
                case 'general':
                    await this.saveGeneralSettings();
                    break;
                case 'company':
                    await this.saveCompanySettings();
                    break;
                case 'orders':
                    await this.saveOrdersSettings();
                    break;
                default:
                    toastManager.show('تم حفظ الإعدادات المتاحة', 'success');
            }

        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            toastManager.show('حدث خطأ في حفظ الإعدادات', 'error');
        }
    }

    /**
     * إعادة تعيين الإعدادات للافتراضية
     * Reset to default settings
     */
    resetToDefaults() {
        modalManager.confirm(
            'تأكيد إعادة التعيين',
            'هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟',
            async () => {
                try {
                    // حذف جميع الإعدادات الحالية
                    const currentSettings = await db.getAll('settings');
                    for (const setting of currentSettings) {
                        await db.delete('settings', setting.id);
                    }

                    // إعادة تحميل الإعدادات الافتراضية
                    this.settings = {};
                    this.setDefaultSettings();

                    toastManager.show('تم إعادة تعيين الإعدادات بنجاح', 'success');
                    await this.refresh();

                } catch (error) {
                    console.error('خطأ في إعادة تعيين الإعدادات:', error);
                    toastManager.show('حدث خطأ في إعادة تعيين الإعدادات', 'error');
                }
            }
        );
    }

    /**
     * تصدير الإعدادات
     * Export settings
     */
    exportSettings() {
        try {
            const settingsData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                settings: this.settings
            };

            const dataStr = JSON.stringify(settingsData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `settings_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.json`;
            link.click();

            toastManager.show('تم تصدير الإعدادات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير الإعدادات:', error);
            toastManager.show('حدث خطأ في تصدير الإعدادات', 'error');
        }
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadSettings();
        await this.loadData();
        this.render();
    }
}

const settingsPage = new SettingsPage();
window.SettingsPage = SettingsPage;
window.settingsPage = settingsPage;
