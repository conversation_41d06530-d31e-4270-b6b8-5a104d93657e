/**
 * صفحة الإعدادات
 * Settings Page
 */

class SettingsPage {
    constructor() {
        this.container = document.getElementById('settings-page');
    }

    async init() {
        this.render();
    }

    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>الإعدادات</h2>
            </div>
            <div class="coming-soon">
                <i class="fas fa-cog fa-3x"></i>
                <h3>قريباً</h3>
                <p>سيتم إضافة صفحة الإعدادات قريباً</p>
            </div>
        `;
    }

    async refresh() {
        this.render();
    }
}

const settingsPage = new SettingsPage();
window.SettingsPage = SettingsPage;
window.settingsPage = settingsPage;
