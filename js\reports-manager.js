/**
 * مدير التقارير - إنشاء وتصدير التقارير المختلفة
 * Reports Manager - Generate and export various reports
 */

class ReportsManager {
    constructor() {
        this.reportTypes = {
            'daily_summary': 'تقرير يومي موجز',
            'courier_performance': 'تقرير أداء المندوبين',
            'company_analysis': 'تحليل الشركات',
            'financial_summary': 'الملخص المالي',
            'returns_analysis': 'تحليل الرواجع',
            'orders_detailed': 'تقرير الطلبات المفصل'
        };
    }

    /**
     * إنشاء تقرير يومي موجز
     * Generate daily summary report
     */
    async generateDailySummaryReport(date = null) {
        try {
            const targetDate = date ? new Date(date) : new Date();
            const statement = await accountingManager.generateDailyStatement(targetDate);
            
            // إضافة إحصائيات إضافية
            const returnsStats = await returnsManager.getReturnsStatistics(
                statement.date, 
                statement.date
            );

            return {
                type: 'daily_summary',
                title: 'التقرير اليومي الموجز',
                date: statement.date,
                summary: statement.summary,
                courierStats: statement.courierStats,
                returnsStats: returnsStats,
                generatedAt: new Date(),
                generatedBy: app.currentUser?.name || 'النظام'
            };

        } catch (error) {
            console.error('خطأ في إنشاء التقرير اليومي:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير أداء المندوبين
     * Generate courier performance report
     */
    async generateCourierPerformanceReport(dateFrom = null, dateTo = null) {
        try {
            const couriers = await db.getAll('couriers', c => c.isActive);
            const performanceData = [];

            for (const courier of couriers) {
                // الحصول على طلبات المندوب
                let orders = await db.getByIndex('orders', 'courierId', courier.id);
                
                // تطبيق فلتر التاريخ
                if (dateFrom) {
                    const fromDate = new Date(dateFrom);
                    orders = orders.filter(order => new Date(order.createdDate) >= fromDate);
                }
                
                if (dateTo) {
                    const toDate = new Date(dateTo);
                    toDate.setHours(23, 59, 59, 999);
                    orders = orders.filter(order => new Date(order.createdDate) <= toDate);
                }

                // حساب الإحصائيات
                const totalOrders = orders.length;
                const deliveredOrders = orders.filter(o => o.status === 'delivered').length;
                const returnedOrders = orders.filter(o => o.status === 'returned' || o.status === 'partial_return').length;
                const pendingOrders = orders.filter(o => o.status === 'pending').length;
                
                const successRate = totalOrders > 0 ? (deliveredOrders / totalOrders * 100) : 0;
                const returnRate = totalOrders > 0 ? (returnedOrders / totalOrders * 100) : 0;

                // حساب العمولات
                const commissions = await accountingManager.calculateCourierCommissions(
                    courier.id, dateFrom, dateTo
                );

                // الحصول على الرواجع المعلقة
                const pendingReturns = await returnsManager.getCourierReturns(courier.id, false);

                performanceData.push({
                    courier: courier,
                    totalOrders: totalOrders,
                    deliveredOrders: deliveredOrders,
                    returnedOrders: returnedOrders,
                    pendingOrders: pendingOrders,
                    successRate: successRate,
                    returnRate: returnRate,
                    totalCommission: commissions.totalCommission,
                    pendingReturns: pendingReturns.length,
                    averageOrderValue: totalOrders > 0 ? (commissions.totalAmount / totalOrders) : 0
                });
            }

            // ترتيب حسب معدل النجاح
            performanceData.sort((a, b) => b.successRate - a.successRate);

            return {
                type: 'courier_performance',
                title: 'تقرير أداء المندوبين',
                dateFrom: dateFrom,
                dateTo: dateTo,
                totalCouriers: performanceData.length,
                averageSuccessRate: performanceData.length > 0 ? 
                    (performanceData.reduce((sum, item) => sum + item.successRate, 0) / performanceData.length) : 0,
                couriers: performanceData,
                generatedAt: new Date(),
                generatedBy: app.currentUser?.name || 'النظام'
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير أداء المندوبين:', error);
            throw error;
        }
    }

    /**
     * إنشاء تحليل الشركات
     * Generate company analysis report
     */
    async generateCompanyAnalysisReport(dateFrom = null, dateTo = null) {
        try {
            const companies = await db.getAll('companies', c => c.isActive);
            const analysisData = [];

            for (const company of companies) {
                // حساب الأرباح
                const profits = await accountingManager.calculateCompanyProfits(
                    company.id, dateFrom, dateTo
                );

                // الحصول على الرواجع
                const returns = await returnsManager.getCompanyReturns(company.id);
                const returnsInPeriod = returns.filter(r => {
                    const returnDate = new Date(r.returnDate);
                    const inRange = (!dateFrom || returnDate >= new Date(dateFrom)) &&
                                   (!dateTo || returnDate <= new Date(dateTo));
                    return inRange;
                });

                // حساب معدل الرواجع
                const returnRate = profits.totalOrders > 0 ? 
                    (returnsInPeriod.length / profits.totalOrders * 100) : 0;

                analysisData.push({
                    company: company,
                    totalOrders: profits.totalOrders,
                    totalRevenue: profits.totalRevenue,
                    totalProfit: profits.totalProfit,
                    profitMargin: profits.profitMargin,
                    averageOrderValue: profits.totalOrders > 0 ? 
                        (profits.totalRevenue / profits.totalOrders) : 0,
                    totalReturns: returnsInPeriod.length,
                    returnRate: returnRate,
                    customerSatisfaction: Math.max(0, 100 - returnRate) // تقدير بسيط لرضا العملاء
                });
            }

            // ترتيب حسب إجمالي الربح
            analysisData.sort((a, b) => b.totalProfit - a.totalProfit);

            return {
                type: 'company_analysis',
                title: 'تحليل الشركات',
                dateFrom: dateFrom,
                dateTo: dateTo,
                totalCompanies: analysisData.length,
                totalRevenue: analysisData.reduce((sum, item) => sum + item.totalRevenue, 0),
                totalProfit: analysisData.reduce((sum, item) => sum + item.totalProfit, 0),
                companies: analysisData,
                generatedAt: new Date(),
                generatedBy: app.currentUser?.name || 'النظام'
            };

        } catch (error) {
            console.error('خطأ في إنشاء تحليل الشركات:', error);
            throw error;
        }
    }

    /**
     * إنشاء الملخص المالي
     * Generate financial summary report
     */
    async generateFinancialSummaryReport(dateFrom = null, dateTo = null) {
        try {
            // الحصول على الأرباح الإجمالية
            const totalProfits = await accountingManager.calculateTotalProfits(dateFrom, dateTo);
            
            // الحصول على تقرير أرباح المندوبين
            const couriersProfits = await accountingManager.generateCouriersProfitReport(dateFrom, dateTo);
            
            // الحصول على تقرير أرباح الشركات
            const companiesProfits = await accountingManager.generateCompaniesProfitReport(dateFrom, dateTo);

            // إحصائيات الرواجع
            const returnsStats = await returnsManager.getReturnsStatistics(dateFrom, dateTo);

            // حساب التكاليف التشغيلية (العمولات + رسوم الرواجع)
            const operatingCosts = totalProfits.totalCommissions;
            const netProfit = totalProfits.totalProfit;

            return {
                type: 'financial_summary',
                title: 'الملخص المالي',
                dateFrom: dateFrom,
                dateTo: dateTo,
                revenue: {
                    totalRevenue: totalProfits.totalRevenue,
                    averageOrderValue: totalProfits.totalOrders > 0 ? 
                        (totalProfits.totalRevenue / totalProfits.totalOrders) : 0
                },
                costs: {
                    totalCommissions: totalProfits.totalCommissions,
                    operatingCosts: operatingCosts,
                    costRatio: totalProfits.totalRevenue > 0 ? 
                        (operatingCosts / totalProfits.totalRevenue * 100) : 0
                },
                profit: {
                    grossProfit: totalProfits.totalRevenue,
                    netProfit: netProfit,
                    profitMargin: totalProfits.profitMargin
                },
                performance: {
                    totalOrders: totalProfits.totalOrders,
                    totalReturns: returnsStats.totalReturns,
                    returnRate: totalProfits.totalOrders > 0 ? 
                        (returnsStats.totalReturns / totalProfits.totalOrders * 100) : 0,
                    activeCouriers: couriersProfits.totalCouriers,
                    activeCompanies: companiesProfits.totalCompanies
                },
                generatedAt: new Date(),
                generatedBy: app.currentUser?.name || 'النظام'
            };

        } catch (error) {
            console.error('خطأ في إنشاء الملخص المالي:', error);
            throw error;
        }
    }

    /**
     * إنشاء تحليل الرواجع
     * Generate returns analysis report
     */
    async generateReturnsAnalysisReport(dateFrom = null, dateTo = null) {
        try {
            const returnsStats = await returnsManager.getReturnsStatistics(dateFrom, dateTo);
            
            // تحليل الرواجع حسب المندوب
            const courierReturnsAnalysis = {};
            const returns = await db.getAll('returns');
            
            for (const returnRecord of returns) {
                // تطبيق فلتر التاريخ
                const returnDate = new Date(returnRecord.returnDate);
                if (dateFrom && returnDate < new Date(dateFrom)) continue;
                if (dateTo && returnDate > new Date(dateTo)) continue;

                if (!courierReturnsAnalysis[returnRecord.courierId]) {
                    const courier = await db.getById('couriers', returnRecord.courierId);
                    courierReturnsAnalysis[returnRecord.courierId] = {
                        courier: courier,
                        totalReturns: 0,
                        returnsByType: {},
                        totalAmount: 0
                    };
                }

                const analysis = courierReturnsAnalysis[returnRecord.courierId];
                analysis.totalReturns++;
                
                const returnType = returnsManager.returnTypes[returnRecord.returnType] || returnRecord.returnType;
                analysis.returnsByType[returnType] = (analysis.returnsByType[returnType] || 0) + 1;

                // إضافة مبلغ الطلب
                if (returnRecord.orderDetails) {
                    analysis.totalAmount += returnRecord.orderDetails.amount || 0;
                }
            }

            return {
                type: 'returns_analysis',
                title: 'تحليل الرواجع',
                dateFrom: dateFrom,
                dateTo: dateTo,
                summary: returnsStats,
                courierAnalysis: Object.values(courierReturnsAnalysis)
                    .sort((a, b) => b.totalReturns - a.totalReturns),
                generatedAt: new Date(),
                generatedBy: app.currentUser?.name || 'النظام'
            };

        } catch (error) {
            console.error('خطأ في إنشاء تحليل الرواجع:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير الطلبات المفصل
     * Generate detailed orders report
     */
    async generateDetailedOrdersReport(filters = {}) {
        try {
            const orders = await dataManager.searchOrders(filters);
            
            // إضافة تفاصيل إضافية لكل طلب
            const detailedOrders = [];
            for (const order of orders) {
                let companyName = 'غير محدد';
                let courierName = 'غير مُسند';

                if (order.companyId) {
                    const company = await db.getById('companies', order.companyId);
                    if (company) companyName = company.name;
                }

                if (order.courierId) {
                    const courier = await db.getById('couriers', order.courierId);
                    if (courier) courierName = courier.name;
                }

                detailedOrders.push({
                    ...order,
                    companyName: companyName,
                    courierName: courierName,
                    statusText: this.getOrderStatusText(order.status),
                    formattedAmount: utils.formatCurrency(order.amount),
                    formattedDate: utils.formatDate(order.createdDate, 'DD/MM/YYYY HH:mm')
                });
            }

            // حساب الإحصائيات
            const totalAmount = orders.reduce((sum, order) => sum + (order.amount || 0), 0);
            const statusCounts = {};
            orders.forEach(order => {
                statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;
            });

            return {
                type: 'orders_detailed',
                title: 'تقرير الطلبات المفصل',
                filters: filters,
                summary: {
                    totalOrders: orders.length,
                    totalAmount: totalAmount,
                    statusCounts: statusCounts
                },
                orders: detailedOrders,
                generatedAt: new Date(),
                generatedBy: app.currentUser?.name || 'النظام'
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير الطلبات المفصل:', error);
            throw error;
        }
    }

    /**
     * تصدير التقرير إلى PDF
     * Export report to PDF
     */
    async exportToPDF(reportData, filename = null) {
        try {
            // هذه الوظيفة تحتاج إلى مكتبة jsPDF
            // سيتم تنفيذها لاحقاً مع إضافة المكتبة
            console.log('تصدير PDF:', reportData.title);
            toastManager.show('سيتم إضافة تصدير PDF قريباً', 'info');
            
        } catch (error) {
            console.error('خطأ في تصدير PDF:', error);
            throw error;
        }
    }

    /**
     * تصدير التقرير إلى Excel
     * Export report to Excel
     */
    async exportToExcel(reportData, filename = null) {
        try {
            const exportFilename = filename || `${reportData.type}_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`;
            
            let csvData = [];
            
            // إضافة معلومات التقرير
            csvData.push(['نوع التقرير', reportData.title]);
            csvData.push(['تاريخ الإنشاء', utils.formatDate(reportData.generatedAt, 'DD/MM/YYYY HH:mm')]);
            csvData.push(['أنشأ بواسطة', reportData.generatedBy]);
            csvData.push([]); // سطر فارغ

            // إضافة البيانات حسب نوع التقرير
            switch (reportData.type) {
                case 'courier_performance':
                    csvData.push(['المندوب', 'إجمالي الطلبات', 'طلبات مسلمة', 'طلبات راجعة', 'معدل النجاح %', 'إجمالي العمولة']);
                    reportData.couriers.forEach(item => {
                        csvData.push([
                            item.courier.name,
                            item.totalOrders,
                            item.deliveredOrders,
                            item.returnedOrders,
                            item.successRate.toFixed(2),
                            utils.formatCurrency(item.totalCommission)
                        ]);
                    });
                    break;

                case 'company_analysis':
                    csvData.push(['الشركة', 'إجمالي الطلبات', 'إجمالي الإيرادات', 'إجمالي الربح', 'هامش الربح %', 'معدل الرواجع %']);
                    reportData.companies.forEach(item => {
                        csvData.push([
                            item.company.name,
                            item.totalOrders,
                            utils.formatCurrency(item.totalRevenue),
                            utils.formatCurrency(item.totalProfit),
                            item.profitMargin.toFixed(2),
                            item.returnRate.toFixed(2)
                        ]);
                    });
                    break;

                case 'orders_detailed':
                    csvData.push(['رقم الوصل', 'الشركة', 'اسم الزبون', 'المبلغ', 'المندوب', 'الحالة', 'التاريخ']);
                    reportData.orders.forEach(order => {
                        csvData.push([
                            order.receiptNumber,
                            order.companyName,
                            order.customerName,
                            order.formattedAmount,
                            order.courierName,
                            order.statusText,
                            order.formattedDate
                        ]);
                    });
                    break;
            }

            // تصدير البيانات
            utils.exportToCSV(csvData.map(row => 
                row.reduce((obj, cell, index) => {
                    obj[`col_${index}`] = cell;
                    return obj;
                }, {})
            ), exportFilename);

            toastManager.show('تم تصدير التقرير بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير Excel:', error);
            throw error;
        }
    }

    /**
     * الحصول على نص حالة الطلب
     * Get order status text
     */
    getOrderStatusText(status) {
        const statusTexts = {
            'delivered': 'مُسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي',
            'delayed': 'مؤجل'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * طباعة التقرير
     * Print report
     */
    printReport(reportData) {
        try {
            // إنشاء نافذة طباعة
            const printWindow = window.open('', '_blank');
            const printContent = this.generatePrintHTML(reportData);
            
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
            
        } catch (error) {
            console.error('خطأ في طباعة التقرير:', error);
            throw error;
        }
    }

    /**
     * إنشاء HTML للطباعة
     * Generate HTML for printing
     */
    generatePrintHTML(reportData) {
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${reportData.title}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .info { margin-bottom: 15px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${reportData.title}</h1>
                    <p>تاريخ الإنشاء: ${utils.formatDate(reportData.generatedAt, 'DD/MM/YYYY HH:mm')}</p>
                </div>
                <!-- محتوى التقرير سيتم إضافته هنا -->
            </body>
            </html>
        `;
    }
}

// إنشاء مثيل مدير التقارير
const reportsManager = new ReportsManager();

// تصدير مدير التقارير
window.reportsManager = reportsManager;
window.ReportsManager = ReportsManager;
