# نظام إدارة شركات التوصيل - الإصدار المحاسبي المتكامل

## 📋 نظرة عامة

نظام شامل لإدارة عمليات شركات التوصيل مع نظام محاسبي متكامل يغطي جميع الجوانب التشغيلية والإدارية والمالية.

## ✨ المميزات الرئيسية

### 🧑‍💼 إدارة المندوبين
- إضافة وتعديل وحذف المندوبين
- ربط المندوبين بالمناطق الجغرافية
- تحديد العمولة الثابتة لكل مندوب
- متابعة أداء المندوبين وحساب العمولات تلقائياً
- تتبع الطلبات المُسندة لكل مندوب

### 📦 إدارة الطلبات
- إنشاء طلبات جديدة مع جميع التفاصيل
- تتبع حالات الطلبات (مسلم، راجع، راجع جزئي، مؤجل)
- إسناد الطلبات للمندوبين بشكل فردي أو جماعي
- البحث والفلترة المتقدمة للطلبات
- عرض تاريخ كامل لكل طلب

### 🏢 إدارة الشركات والعملاء
- إدارة الشركات والكيانات التجارية
- ربط الطلبات بالشركات
- حساب الفواتير والمستحقات
- إدارة عملاء الأفراد

### 🔄 نظام إدارة الرواجع المتقدم
- تصنيف الرواجع (كلي، جزئي، مؤجل)
- استلام الرواجع من المندوبين
- تتبع الرواجع في ذمة المندوب
- تحويل الرواجع إلى المركز
- كشوفات الرواجع للشركات
- تقارير ديون الرواجع للمندوبين

### 💰 النظام المحاسبي المتكامل
- حساب العمولات تلقائياً
- حساب الأرباح والخسائر
- كشوفات حساب يومية وشهرية
- تتبع المعاملات المالية
- حساب أرصدة المندوبين

### 📊 نظام التقارير الشامل
- تقارير يومية موجزة
- تقارير أداء المندوبين
- تحليل الشركات والعملاء
- الملخص المالي
- تحليل الرواجع
- تقارير الطلبات المفصلة
- تصدير التقارير (Excel/PDF)

### 🗄️ أرشفة الطلبات
- نقل الطلبات القديمة للأرشيف
- البحث في الأرشيف
- الحفاظ على البيانات للمراجعة

### 🔐 نظام الصلاحيات
- مستويات مختلفة من المستخدمين
- تتبع أنشطة المستخدمين
- حماية البيانات الحساسة

## 🛠️ التقنيات المستخدمة

- **الواجهة الأمامية**: HTML5, CSS3, JavaScript (Vanilla)
- **التصميم**: CSS Grid/Flexbox مع دعم RTL + تصميم نيومورفيك
- **قاعدة البيانات**: IndexedDB للتخزين المحلي
- **الخطوط**: Cairo Font للنصوص العربية
- **الأيقونات**: Font Awesome
- **التصدير**: CSV/Excel support

## 📁 هيكل المشروع

```
delivery-system/
├── index.html                 # الصفحة الرئيسية
├── css/
│   ├── style.css             # الأنماط الأساسية
│   ├── components.css        # أنماط المكونات
│   └── responsive.css        # التصميم المتجاوب
├── js/
│   ├── app.js               # التطبيق الرئيسي
│   ├── database.js          # إدارة قاعدة البيانات
│   ├── utils.js             # الوظائف المساعدة
│   ├── components.js        # مكونات واجهة المستخدم
│   ├── data-manager.js      # مدير البيانات
│   ├── returns-manager.js   # مدير الرواجع
│   ├── accounting-manager.js # مدير المحاسبة
│   ├── reports-manager.js   # مدير التقارير
│   └── pages/              # صفحات النظام
│       ├── dashboard.js     # لوحة التحكم
│       ├── orders.js        # إدارة الطلبات
│       ├── couriers.js      # إدارة المندوبين
│       ├── companies.js     # إدارة الشركات
│       ├── returns.js       # إدارة الرواجع
│       ├── accounting.js    # المحاسبة
│       ├── reports.js       # التقارير
│       ├── archive.js       # الأرشيف
│       └── settings.js      # الإعدادات
└── README.md               # هذا الملف
```

## 🚀 التشغيل

1. **تحميل الملفات**: قم بتحميل جميع ملفات المشروع
2. **فتح المتصفح**: افتح ملف `index.html` في متصفح حديث
3. **التهيئة الأولية**: سيتم تهيئة قاعدة البيانات تلقائياً
4. **تسجيل الدخول**: استخدم المستخدم الافتراضي:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## 📱 التوافق

- **المتصفحات**: Chrome, Firefox, Safari, Edge (الإصدارات الحديثة)
- **الأجهزة**: متجاوب مع الموبايل والتابلت والكمبيوتر
- **اللغة**: دعم كامل للغة العربية مع RTL

## 🔧 الإعدادات

يمكن تخصيص النظام من خلال صفحة الإعدادات:
- العمولة الافتراضية للمندوبين
- اسم الشركة
- العملة المستخدمة
- تنسيق التاريخ
- المناطق الجغرافية

## 📊 قاعدة البيانات

النظام يستخدم IndexedDB مع الجداول التالية:
- `couriers` - المندوبين
- `companies` - الشركات
- `regions` - المناطق
- `orders` - الطلبات
- `returns` - الرواجع
- `accounting` - المحاسبة
- `order_history` - تاريخ الطلبات
- `settings` - الإعدادات
- `users` - المستخدمين

## 🔒 الأمان

- تشفير كلمات المرور (يُنصح بتطبيقه في الإنتاج)
- نظام صلاحيات متدرج
- تتبع أنشطة المستخدمين
- حماية البيانات الحساسة

## 📈 الميزات المستقبلية

- [ ] تصدير PDF للتقارير
- [ ] إشعارات الدفع (Push Notifications)
- [ ] تطبيق موبايل
- [ ] تزامن البيانات مع السحابة
- [ ] تحليلات متقدمة بالذكاء الاصطناعي
- [ ] واجهة برمجة التطبيقات (API)

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يرجى:
1. التحقق من وحدة تحكم المتصفح للأخطاء
2. التأكد من تحديث المتصفح
3. مسح ذاكرة التخزين المؤقت

## 📝 الترخيص

هذا المشروع مطور خصيصاً لشركات التوصيل ويمكن تخصيصه حسب الاحتياجات.

## 👥 المساهمة

نرحب بالمساهمات لتحسين النظام:
- إضافة ميزات جديدة
- إصلاح الأخطاء
- تحسين الأداء
- تحسين التصميم

## 📞 الدعم الفني

للحصول على الدعم الفني أو التخصيص:
- تحقق من الوثائق أولاً
- راجع الأسئلة الشائعة
- تواصل مع فريق التطوير

---

**تم تطوير هذا النظام بعناية لتلبية احتياجات شركات التوصيل في المنطقة العربية مع مراعاة الخصوصيات المحلية والمتطلبات التشغيلية.**
