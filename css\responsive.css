/* ===== Responsive Design ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 3fr 1fr;
    }
    
    .stats-row {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    :root {
        --sidebar-width: 250px;
    }
    
    .dashboard-content {
        grid-template-columns: 2fr 1fr;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    :root {
        --sidebar-width: 220px;
        --spacing-6: 1.25rem;
    }
    
    .header-stats {
        display: none;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .page-content {
        padding: var(--spacing-4);
    }
    
    .modal {
        max-width: 95vw;
        margin: var(--spacing-4);
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform var(--transition-normal);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .header {
        margin: var(--spacing-2) 0 var(--spacing-2) var(--spacing-2);
        padding: 0 var(--spacing-4);
    }
    
    #page-title {
        font-size: var(--font-size-xl);
    }
    
    .stats-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .stat-card {
        padding: var(--spacing-4);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .stat-info h3 {
        font-size: var(--font-size-2xl);
    }
    
    .dashboard-section {
        padding: var(--spacing-4);
    }
    
    .quick-actions {
        gap: var(--spacing-2);
    }
    
    .action-btn {
        padding: var(--spacing-3);
        font-size: var(--font-size-sm);
    }
    
    .table-container {
        padding: var(--spacing-4);
        overflow-x: auto;
    }
    
    .table {
        min-width: 600px;
    }
    
    .modal {
        max-width: 95vw;
        max-height: 95vh;
        margin: var(--spacing-2);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-4);
    }
    
    .toast-container {
        top: var(--spacing-4);
        left: var(--spacing-4);
        right: var(--spacing-4);
    }
    
    .toast {
        min-width: auto;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    :root {
        --font-size-base: 0.9rem;
        --spacing-4: 0.75rem;
        --spacing-6: 1rem;
    }
    
    .header {
        height: 60px;
        margin: var(--spacing-2);
        padding: 0 var(--spacing-3);
    }
    
    #page-title {
        font-size: var(--font-size-lg);
    }
    
    .page-content {
        padding: var(--spacing-3);
    }
    
    .stat-card {
        padding: var(--spacing-3);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-2);
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
    
    .stat-info h3 {
        font-size: var(--font-size-xl);
    }
    
    .dashboard-section {
        padding: var(--spacing-3);
    }
    
    .dashboard-section h3 {
        font-size: var(--font-size-lg);
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .order-status {
        align-self: flex-end;
    }
    
    .action-btn {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-sm);
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        padding: var(--spacing-2);
        font-size: var(--font-size-sm);
    }
    
    .table th,
    .table td {
        padding: var(--spacing-2);
        font-size: var(--font-size-sm);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-3);
    }
    
    .modal-title {
        font-size: var(--font-size-lg);
    }
    
    .toast {
        padding: var(--spacing-3);
        font-size: var(--font-size-sm);
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .mobile-menu-toggle,
    .action-btn,
    .modal-overlay,
    .toast-container {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
    }
    
    .page-content {
        padding: 0;
    }
    
    .table-container {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    .stat-card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    body {
        background: white;
        color: black;
    }
    
    .page {
        page-break-inside: avoid;
    }
    
    .table {
        page-break-inside: auto;
    }
    
    .table tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }
    
    .table thead {
        display: table-header-group;
    }
    
    .table tfoot {
        display: table-footer-group;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --bg-primary: #ffffff;
        --bg-secondary: #f8f9fa;
        --gray-100: #e9ecef;
        --gray-200: #dee2e6;
        --gray-300: #ced4da;
        --shadow-light: #ffffff;
        --shadow-dark: #adb5bd;
    }
    
    .sidebar,
    .header,
    .stat-card,
    .dashboard-section,
    .table-container {
        border: 2px solid var(--gray-300);
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        border: 2px solid var(--gray-400);
    }
    
    .action-btn {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-spinner i {
        animation: none;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here in future updates */
}

/* Landscape Orientation on Mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .sidebar {
        width: 200px;
    }
    
    .header {
        height: 50px;
    }
    
    .stat-card {
        flex-direction: row;
        text-align: right;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr 1fr;
    }
}

/* Very Large Screens (1400px and up) */
@media (min-width: 1400px) {
    .app-container {
        max-width: 1400px;
        margin: 0 auto;
        box-shadow: var(--shadow-xl);
    }
    
    .stats-row {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-6);
    }
    
    .dashboard-content {
        grid-template-columns: 3fr 1fr;
        gap: var(--spacing-8);
    }
}
