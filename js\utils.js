/**
 * وظائف مساعدة للنظام
 * Utility functions for the system
 */

/**
 * تنسيق التاريخ
 * Format date
 */
function formatDate(date, format = 'DD/MM/YYYY') {
    if (!date) return '';
    
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    
    switch (format) {
        case 'DD/MM/YYYY':
            return `${day}/${month}/${year}`;
        case 'DD/MM/YYYY HH:mm':
            return `${day}/${month}/${year} ${hours}:${minutes}`;
        case 'YYYY-MM-DD':
            return `${year}-${month}-${day}`;
        case 'relative':
            return getRelativeTime(d);
        default:
            return `${day}/${month}/${year}`;
    }
}

/**
 * الحصول على الوقت النسبي
 * Get relative time
 */
function getRelativeTime(date) {
    const now = new Date();
    const diff = now - date;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
        return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
    } else if (hours > 0) {
        return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
    } else if (minutes > 0) {
        return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
    } else {
        return 'الآن';
    }
}

/**
 * تنسيق المبلغ المالي
 * Format currency
 */
function formatCurrency(amount, currency = 'ر.س') {
    if (isNaN(amount)) return '0 ' + currency;
    
    const formatted = parseFloat(amount).toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    
    return `${formatted} ${currency}`;
}

/**
 * تنسيق رقم الهاتف
 * Format phone number
 */
function formatPhone(phone) {
    if (!phone) return '';
    
    // إزالة جميع الأحرف غير الرقمية
    const cleaned = phone.replace(/\D/g, '');
    
    // تنسيق الرقم السعودي
    if (cleaned.startsWith('966')) {
        return cleaned.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
    } else if (cleaned.startsWith('05')) {
        return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '$1 $2 $3');
    }
    
    return phone;
}

/**
 * التحقق من صحة رقم الهاتف
 * Validate phone number
 */
function validatePhone(phone) {
    const cleaned = phone.replace(/\D/g, '');
    return /^(05\d{8}|966\d{9})$/.test(cleaned);
}

/**
 * التحقق من صحة البريد الإلكتروني
 * Validate email
 */
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * إنشاء معرف فريد
 * Generate unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * إنشاء رقم وصل
 * Generate receipt number
 */
function generateReceiptNumber() {
    const date = new Date();
    const year = date.getFullYear().toString().substr(-2);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    
    return `${year}${month}${day}${random}`;
}

/**
 * تنظيف النص
 * Clean text
 */
function cleanText(text) {
    if (!text) return '';
    return text.toString().trim().replace(/\s+/g, ' ');
}

/**
 * البحث في النص
 * Search in text
 */
function searchInText(text, query) {
    if (!text || !query) return false;
    
    const cleanText = cleanText(text).toLowerCase();
    const cleanQuery = cleanText(query).toLowerCase();
    
    return cleanText.includes(cleanQuery);
}

/**
 * فلترة المصفوفة
 * Filter array
 */
function filterArray(array, filters) {
    if (!array || !filters) return array;
    
    return array.filter(item => {
        return Object.keys(filters).every(key => {
            const filterValue = filters[key];
            const itemValue = item[key];
            
            if (filterValue === null || filterValue === undefined || filterValue === '') {
                return true;
            }
            
            if (typeof filterValue === 'string') {
                return searchInText(itemValue, filterValue);
            }
            
            return itemValue === filterValue;
        });
    });
}

/**
 * ترتيب المصفوفة
 * Sort array
 */
function sortArray(array, sortBy, sortOrder = 'asc') {
    if (!array || !sortBy) return array;
    
    return [...array].sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];
        
        // التعامل مع التواريخ
        if (aValue instanceof Date && bValue instanceof Date) {
            aValue = aValue.getTime();
            bValue = bValue.getTime();
        }
        
        // التعامل مع النصوص
        if (typeof aValue === 'string' && typeof bValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }
        
        if (sortOrder === 'desc') {
            return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
    });
}

/**
 * تجميع المصفوفة
 * Group array
 */
function groupArray(array, groupBy) {
    if (!array || !groupBy) return {};
    
    return array.reduce((groups, item) => {
        const key = item[groupBy];
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(item);
        return groups;
    }, {});
}

/**
 * حساب الإحصائيات
 * Calculate statistics
 */
function calculateStats(array, field) {
    if (!array || !field) return { sum: 0, avg: 0, min: 0, max: 0, count: 0 };
    
    const values = array.map(item => parseFloat(item[field]) || 0);
    const sum = values.reduce((total, value) => total + value, 0);
    const count = values.length;
    const avg = count > 0 ? sum / count : 0;
    const min = count > 0 ? Math.min(...values) : 0;
    const max = count > 0 ? Math.max(...values) : 0;
    
    return { sum, avg, min, max, count };
}

/**
 * تصدير البيانات إلى CSV
 * Export data to CSV
 */
function exportToCSV(data, filename = 'data.csv') {
    if (!data || data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => 
            headers.map(header => {
                const value = row[header];
                return typeof value === 'string' ? `"${value}"` : value;
            }).join(',')
        )
    ].join('\n');
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * نسخ النص إلى الحافظة
 * Copy text to clipboard
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        // Fallback للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        } catch (err) {
            document.body.removeChild(textArea);
            return false;
        }
    }
}

/**
 * تأخير التنفيذ
 * Delay execution
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * التحقق من كون الجهاز موبايل
 * Check if device is mobile
 */
function isMobile() {
    return window.innerWidth <= 767;
}

/**
 * التحقق من كون الجهاز تابلت
 * Check if device is tablet
 */
function isTablet() {
    return window.innerWidth > 767 && window.innerWidth <= 991;
}

/**
 * التحقق من كون الجهاز ديسكتوب
 * Check if device is desktop
 */
function isDesktop() {
    return window.innerWidth > 991;
}

/**
 * الحصول على معلومات الجهاز
 * Get device info
 */
function getDeviceInfo() {
    return {
        isMobile: isMobile(),
        isTablet: isTablet(),
        isDesktop: isDesktop(),
        width: window.innerWidth,
        height: window.innerHeight,
        userAgent: navigator.userAgent
    };
}

/**
 * تحويل الأرقام الإنجليزية إلى عربية
 * Convert English numbers to Arabic
 */
function toArabicNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.toString().replace(/[0-9]/g, (match) => arabicNumbers[parseInt(match)]);
}

/**
 * تحويل الأرقام العربية إلى إنجليزية
 * Convert Arabic numbers to English
 */
function toEnglishNumbers(str) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.toString().replace(/[٠-٩]/g, (match) => arabicNumbers.indexOf(match));
}

// تصدير الوظائف للاستخدام العام
window.utils = {
    formatDate,
    getRelativeTime,
    formatCurrency,
    formatPhone,
    validatePhone,
    validateEmail,
    generateId,
    generateReceiptNumber,
    cleanText,
    searchInText,
    filterArray,
    sortArray,
    groupArray,
    calculateStats,
    exportToCSV,
    copyToClipboard,
    delay,
    isMobile,
    isTablet,
    isDesktop,
    getDeviceInfo,
    toArabicNumbers,
    toEnglishNumbers
};
