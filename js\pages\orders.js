/**
 * صفحة إدارة الطلبات
 * Orders Management Page
 */

class OrdersPage {
    constructor() {
        this.container = document.getElementById('orders-page');
        this.tableManager = null;
        this.orders = [];
        this.companies = [];
        this.couriers = [];
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة الطلبات:', error);
            toastManager.show('حدث خطأ في تحميل صفحة الطلبات', 'error');
        }
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            this.orders = await db.getAll('orders', order => !order.isArchived);
            this.companies = await db.getAll('companies');
            this.couriers = await db.getAll('couriers');
        } catch (error) {
            console.error('خطأ في تحميل بيانات الطلبات:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>إدارة الطلبات</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="ordersPage.showAddOrderModal()">
                        <i class="fas fa-plus"></i>
                        طلب جديد
                    </button>
                    <button class="action-btn secondary" onclick="ordersPage.showAssignOrdersModal()">
                        <i class="fas fa-user-plus"></i>
                        إسناد طلبات
                    </button>
                    <button class="action-btn info" onclick="ordersPage.exportOrders()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="filters-section">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>البحث</label>
                        <input type="text" id="search-input" placeholder="رقم الوصل، اسم الزبون، أو الهاتف">
                    </div>
                    <div class="filter-group">
                        <label>الشركة</label>
                        <select id="company-filter">
                            <option value="">جميع الشركات</option>
                            ${this.companies.map(company => 
                                `<option value="${company.id}">${company.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>المندوب</label>
                        <select id="courier-filter">
                            <option value="">جميع المندوبين</option>
                            ${this.couriers.map(courier => 
                                `<option value="${courier.id}">${courier.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة</label>
                        <select id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلق</option>
                            <option value="delivered">مُسلم</option>
                            <option value="returned">راجع</option>
                            <option value="partial_return">راجع جزئي</option>
                            <option value="delayed">مؤجل</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="action-btn primary" onclick="ordersPage.applyFilters()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <button class="action-btn secondary" onclick="ordersPage.clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-container" id="orders-table-container">
                <!-- سيتم إدراج الجدول هنا -->
            </div>
        `;

        this.renderTable();
    }

    /**
     * رسم الجدول
     * Render table
     */
    renderTable() {
        const tableContainer = document.getElementById('orders-table-container');
        if (!tableContainer) return;

        // إعداد أعمدة الجدول
        const columns = [
            { key: 'receiptNumber', title: 'رقم الوصل' },
            { key: 'companyName', title: 'الشركة' },
            { key: 'customerName', title: 'اسم الزبون' },
            { key: 'customerPhone', title: 'الهاتف' },
            { key: 'amount', title: 'المبلغ', render: (value) => utils.formatCurrency(value) },
            { key: 'courierName', title: 'المندوب' },
            { key: 'status', title: 'الحالة', render: (value) => this.renderStatusBadge(value) },
            { key: 'createdDate', title: 'التاريخ', render: (value) => utils.formatDate(value, 'DD/MM/YYYY') },
            { key: 'actions', title: 'الإجراءات', render: (value, row) => this.renderActions(row) }
        ];

        // إنشاء مدير الجدول
        this.tableManager = new TableManager('orders-table-container', {
            columns: columns,
            searchable: false, // سنستخدم البحث المخصص
            sortable: true,
            paginated: true,
            pageSize: 20
        });

        // تحضير البيانات للجدول
        this.prepareTableData();
    }

    /**
     * تحضير بيانات الجدول
     * Prepare table data
     */
    async prepareTableData() {
        const tableData = [];

        for (const order of this.orders) {
            // الحصول على اسم الشركة
            const company = this.companies.find(c => c.id === order.companyId);
            const companyName = company ? company.name : 'غير محدد';

            // الحصول على اسم المندوب
            const courier = this.couriers.find(c => c.id === order.courierId);
            const courierName = courier ? courier.name : 'غير مُسند';

            tableData.push({
                ...order,
                companyName: companyName,
                courierName: courierName
            });
        }

        this.tableManager.setData(tableData);
    }

    /**
     * رسم شارة الحالة
     * Render status badge
     */
    renderStatusBadge(status) {
        const statusConfig = {
            'pending': { text: 'معلق', class: 'warning' },
            'delivered': { text: 'مُسلم', class: 'success' },
            'returned': { text: 'راجع', class: 'danger' },
            'partial_return': { text: 'راجع جزئي', class: 'warning' },
            'delayed': { text: 'مؤجل', class: 'info' }
        };

        const config = statusConfig[status] || { text: 'غير محدد', class: 'secondary' };
        return `<span class="badge badge-${config.class}">${config.text}</span>`;
    }

    /**
     * رسم أزرار الإجراءات
     * Render action buttons
     */
    renderActions(order) {
        return `
            <div class="action-buttons">
                <button class="btn-sm btn-primary" onclick="ordersPage.viewOrder(${order.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-sm btn-secondary" onclick="ordersPage.editOrder(${order.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-sm btn-info" onclick="ordersPage.updateStatus(${order.id})" title="تحديث الحالة">
                    <i class="fas fa-sync"></i>
                </button>
                <button class="btn-sm btn-danger" onclick="ordersPage.deleteOrder(${order.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // البحث المباشر
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('keyup', () => {
                this.applyFilters();
            });
        }

        // تطبيق الفلاتر عند تغيير القيم
        ['company-filter', 'courier-filter', 'status-filter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', () => {
                    this.applyFilters();
                });
            }
        });
    }

    /**
     * تطبيق الفلاتر
     * Apply filters
     */
    applyFilters() {
        const searchQuery = document.getElementById('search-input')?.value || '';
        const companyId = document.getElementById('company-filter')?.value || '';
        const courierId = document.getElementById('courier-filter')?.value || '';
        const status = document.getElementById('status-filter')?.value || '';

        const filters = {
            receiptNumber: searchQuery,
            customerName: searchQuery,
            customerPhone: searchQuery,
            companyId: companyId ? parseInt(companyId) : null,
            courierId: courierId ? parseInt(courierId) : null,
            status: status
        };

        // تطبيق الفلاتر على البيانات
        let filteredOrders = [...this.orders];

        if (searchQuery) {
            filteredOrders = filteredOrders.filter(order => 
                order.receiptNumber.includes(searchQuery) ||
                order.customerName.includes(searchQuery) ||
                order.customerPhone.includes(searchQuery)
            );
        }

        if (companyId) {
            filteredOrders = filteredOrders.filter(order => 
                order.companyId === parseInt(companyId)
            );
        }

        if (courierId) {
            filteredOrders = filteredOrders.filter(order => 
                order.courierId === parseInt(courierId)
            );
        }

        if (status) {
            filteredOrders = filteredOrders.filter(order => 
                order.status === status
            );
        }

        // تحديث الجدول
        this.orders = filteredOrders;
        this.prepareTableData();
    }

    /**
     * مسح الفلاتر
     * Clear filters
     */
    async clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('company-filter').value = '';
        document.getElementById('courier-filter').value = '';
        document.getElementById('status-filter').value = '';

        await this.loadData();
        this.prepareTableData();
    }

    /**
     * عرض نافذة إضافة طلب جديد
     * Show add order modal
     */
    showAddOrderModal() {
        showAddOrderModal();
    }

    /**
     * عرض نافذة إسناد الطلبات
     * Show assign orders modal
     */
    showAssignOrdersModal() {
        // سيتم تنفيذها لاحقاً
        toastManager.show('سيتم إضافة هذه الميزة قريباً', 'info');
    }

    /**
     * تصدير الطلبات
     * Export orders
     */
    exportOrders() {
        try {
            const exportData = this.orders.map(order => {
                const company = this.companies.find(c => c.id === order.companyId);
                const courier = this.couriers.find(c => c.id === order.courierId);
                
                return {
                    'رقم الوصل': order.receiptNumber,
                    'الشركة': company ? company.name : 'غير محدد',
                    'اسم الزبون': order.customerName,
                    'هاتف الزبون': order.customerPhone,
                    'العنوان': order.address,
                    'المبلغ': order.amount,
                    'المندوب': courier ? courier.name : 'غير مُسند',
                    'الحالة': this.getStatusText(order.status),
                    'التاريخ': utils.formatDate(order.createdDate, 'DD/MM/YYYY HH:mm'),
                    'الملاحظات': order.notes || ''
                };
            });

            utils.exportToCSV(exportData, `orders_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
            toastManager.show('تم تصدير الطلبات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير الطلبات:', error);
            toastManager.show('حدث خطأ في تصدير الطلبات', 'error');
        }
    }

    /**
     * الحصول على نص الحالة
     * Get status text
     */
    getStatusText(status) {
        const statusTexts = {
            'pending': 'معلق',
            'delivered': 'مُسلم',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي',
            'delayed': 'مؤجل'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * عرض تفاصيل الطلب
     * View order details
     */
    async viewOrder(orderId) {
        // استخدام نفس الدالة من dashboard.js
        if (window.viewOrderDetails) {
            window.viewOrderDetails(orderId);
        }
    }

    /**
     * تعديل الطلب
     * Edit order
     */
    editOrder(orderId) {
        // سيتم تنفيذها لاحقاً
        toastManager.show('سيتم إضافة تعديل الطلبات قريباً', 'info');
    }

    /**
     * تحديث حالة الطلب
     * Update order status
     */
    updateStatus(orderId) {
        // سيتم تنفيذها لاحقاً
        toastManager.show('سيتم إضافة تحديث الحالة قريباً', 'info');
    }

    /**
     * حذف الطلب
     * Delete order
     */
    deleteOrder(orderId) {
        modalManager.confirm(
            'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.',
            async () => {
                try {
                    await db.delete('orders', orderId);
                    toastManager.show('تم حذف الطلب بنجاح', 'success');
                    await this.loadData();
                    this.prepareTableData();
                } catch (error) {
                    console.error('خطأ في حذف الطلب:', error);
                    toastManager.show('حدث خطأ في حذف الطلب', 'error');
                }
            }
        );
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadData();
        this.prepareTableData();
    }
}

// إنشاء مثيل الصفحة
const ordersPage = new OrdersPage();

// تصدير الصفحة
window.OrdersPage = OrdersPage;
window.ordersPage = ordersPage;
