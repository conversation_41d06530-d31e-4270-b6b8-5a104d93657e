/**
 * نظام قاعدة البيانات المحلية باستخدام IndexedDB
 * Database Management System using IndexedDB
 */

class DeliveryDatabase {
    constructor() {
        this.dbName = 'DeliverySystemDB';
        this.dbVersion = 1;
        this.db = null;
        this.isInitialized = false;
    }

    /**
     * تهيئة قاعدة البيانات
     * Initialize the database
     */
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('خطأ في فتح قاعدة البيانات:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                this.isInitialized = true;
                console.log('تم تهيئة قاعدة البيانات بنجاح');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                this.createTables();
            };
        });
    }

    /**
     * إنشاء الجداول
     * Create database tables
     */
    createTables() {
        // جدول المندوبين - Couriers Table
        if (!this.db.objectStoreNames.contains('couriers')) {
            const courierStore = this.db.createObjectStore('couriers', { 
                keyPath: 'id', 
                autoIncrement: true 
            });
            courierStore.createIndex('name', 'name', { unique: false });
            courierStore.createIndex('phone', 'phone', { unique: true });
            courierStore.createIndex('region', 'region', { unique: false });
            courierStore.createIndex('isActive', 'isActive', { unique: false });
        }

        // جدول الشركات - Companies Table
        if (!this.db.objectStoreNames.contains('companies')) {
            const companyStore = this.db.createObjectStore('companies', { 
                keyPath: 'id', 
                autoIncrement: true 
            });
            companyStore.createIndex('name', 'name', { unique: true });
            companyStore.createIndex('phone', 'phone', { unique: false });
            companyStore.createIndex('isActive', 'isActive', { unique: false });
        }

        // جدول المناطق - Regions Table
        if (!this.db.objectStoreNames.contains('regions')) {
            const regionStore = this.db.createObjectStore('regions', { 
                keyPath: 'id', 
                autoIncrement: true 
            });
            regionStore.createIndex('name', 'name', { unique: true });
            regionStore.createIndex('isActive', 'isActive', { unique: false });
        }

        // جدول الطلبات - Orders Table
        if (!this.db.objectStoreNames.contains('orders')) {
            const orderStore = this.db.createObjectStore('orders', { 
                keyPath: 'id', 
                autoIncrement: true 
            });
            orderStore.createIndex('receiptNumber', 'receiptNumber', { unique: true });
            orderStore.createIndex('companyId', 'companyId', { unique: false });
            orderStore.createIndex('courierId', 'courierId', { unique: false });
            orderStore.createIndex('status', 'status', { unique: false });
            orderStore.createIndex('createdDate', 'createdDate', { unique: false });
            orderStore.createIndex('customerName', 'customerName', { unique: false });
            orderStore.createIndex('customerPhone', 'customerPhone', { unique: false });
            orderStore.createIndex('isArchived', 'isArchived', { unique: false });
        }

        // جدول الرواجع - Returns Table
        if (!this.db.objectStoreNames.contains('returns')) {
            const returnStore = this.db.createObjectStore('returns', { 
                keyPath: 'id', 
                autoIncrement: true 
            });
            returnStore.createIndex('orderId', 'orderId', { unique: false });
            returnStore.createIndex('courierId', 'courierId', { unique: false });
            returnStore.createIndex('returnType', 'returnType', { unique: false });
            returnStore.createIndex('returnDate', 'returnDate', { unique: false });
            returnStore.createIndex('isReceived', 'isReceived', { unique: false });
        }

        // جدول المحاسبة - Accounting Table
        if (!this.db.objectStoreNames.contains('accounting')) {
            const accountingStore = this.db.createObjectStore('accounting', { 
                keyPath: 'id', 
                autoIncrement: true 
            });
            accountingStore.createIndex('date', 'date', { unique: false });
            accountingStore.createIndex('type', 'type', { unique: false });
            accountingStore.createIndex('courierId', 'courierId', { unique: false });
            accountingStore.createIndex('companyId', 'companyId', { unique: false });
        }

        // جدول الإعدادات - Settings Table
        if (!this.db.objectStoreNames.contains('settings')) {
            const settingsStore = this.db.createObjectStore('settings', { 
                keyPath: 'key' 
            });
        }

        // جدول المستخدمين - Users Table
        if (!this.db.objectStoreNames.contains('users')) {
            const userStore = this.db.createObjectStore('users', {
                keyPath: 'id',
                autoIncrement: true
            });
            userStore.createIndex('username', 'username', { unique: true });
            userStore.createIndex('role', 'role', { unique: false });
            userStore.createIndex('isActive', 'isActive', { unique: false });
        }

        // جدول تاريخ الطلبات - Order History Table
        if (!this.db.objectStoreNames.contains('order_history')) {
            const historyStore = this.db.createObjectStore('order_history', {
                keyPath: 'id',
                autoIncrement: true
            });
            historyStore.createIndex('orderId', 'orderId', { unique: false });
            historyStore.createIndex('action', 'action', { unique: false });
            historyStore.createIndex('createdDate', 'createdDate', { unique: false });
            historyStore.createIndex('userId', 'userId', { unique: false });
        }

        console.log('تم إنشاء جميع الجداول بنجاح');
        this.initializeDefaultData();
    }

    /**
     * تهيئة البيانات الافتراضية
     * Initialize default data
     */
    async initializeDefaultData() {
        try {
            // إضافة المناطق الافتراضية
            const defaultRegions = [
                { name: 'الرياض', isActive: true, createdDate: new Date() },
                { name: 'جدة', isActive: true, createdDate: new Date() },
                { name: 'الدمام', isActive: true, createdDate: new Date() },
                { name: 'مكة المكرمة', isActive: true, createdDate: new Date() },
                { name: 'المدينة المنورة', isActive: true, createdDate: new Date() }
            ];

            for (const region of defaultRegions) {
                await this.add('regions', region);
            }

            // إضافة الإعدادات الافتراضية
            const defaultSettings = [
                { key: 'defaultCommission', value: 5 },
                { key: 'companyName', value: 'شركة التوصيل السريع' },
                { key: 'currency', value: 'ر.س' },
                { key: 'dateFormat', value: 'DD/MM/YYYY' },
                { key: 'language', value: 'ar' },
                { key: 'theme', value: 'light' }
            ];

            for (const setting of defaultSettings) {
                await this.add('settings', setting);
            }

            // إضافة مستخدم افتراضي (مدير)
            const defaultUser = {
                username: 'admin',
                password: 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
                role: 'admin',
                name: 'المدير العام',
                isActive: true,
                createdDate: new Date()
            };

            await this.add('users', defaultUser);

            console.log('تم تهيئة البيانات الافتراضية بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة البيانات الافتراضية:', error);
        }
    }

    /**
     * إضافة سجل جديد
     * Add new record
     */
    async add(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // إضافة تاريخ الإنشاء إذا لم يكن موجوداً
            if (!data.createdDate) {
                data.createdDate = new Date();
            }
            
            const request = store.add(data);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * تحديث سجل موجود
     * Update existing record
     */
    async update(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // إضافة تاريخ التحديث
            data.updatedDate = new Date();
            
            const request = store.put(data);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * حذف سجل
     * Delete record
     */
    async delete(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * الحصول على سجل بواسطة المعرف
     * Get record by ID
     */
    async getById(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * الحصول على جميع السجلات
     * Get all records
     */
    async getAll(storeName, filter = null) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                let results = request.result;
                
                // تطبيق الفلتر إذا كان موجوداً
                if (filter && typeof filter === 'function') {
                    results = results.filter(filter);
                }
                
                resolve(results);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * البحث بواسطة فهرس
     * Search by index
     */
    async getByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * عد السجلات
     * Count records
     */
    async count(storeName, filter = null) {
        const records = await this.getAll(storeName, filter);
        return records.length;
    }

    /**
     * إغلاق قاعدة البيانات
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            this.isInitialized = false;
        }
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const db = new DeliveryDatabase();

// تصدير قاعدة البيانات للاستخدام في الملفات الأخرى
window.db = db;
