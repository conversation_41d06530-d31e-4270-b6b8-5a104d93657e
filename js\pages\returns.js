/**
 * صفحة إدارة الرواجع
 * Returns Management Page
 */

class ReturnsPage {
    constructor() {
        this.container = document.getElementById('returns-page');
        this.tableManager = null;
        this.returns = [];
        this.orders = [];
        this.couriers = [];
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة الرواجع:', error);
            toastManager.show('حدث خطأ في تحميل صفحة الرواجع', 'error');
        }
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            this.returns = await db.getAll('returns');
            this.orders = await db.getAll('orders');
            this.couriers = await db.getAll('couriers');
        } catch (error) {
            console.error('خطأ في تحميل بيانات الرواجع:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        const stats = this.calculateStats();

        this.container.innerHTML = `
            <div class="page-header">
                <h2>إدارة الرواجع</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="returnsPage.showReceiveReturnsModal()">
                        <i class="fas fa-hand-holding"></i>
                        استلام رواجع
                    </button>
                    <button class="action-btn secondary" onclick="returnsPage.showCourierReturnsModal()">
                        <i class="fas fa-user-clock"></i>
                        رواجع المندوبين
                    </button>
                    <button class="action-btn info" onclick="returnsPage.exportReturns()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${stats.totalReturns}</h3>
                        <p>إجمالي الرواجع</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${stats.pendingReturns}</h3>
                        <p>رواجع معلقة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${stats.receivedReturns}</h3>
                        <p>رواجع مستلمة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${stats.overdueReturns}</h3>
                        <p>رواجع متأخرة</p>
                    </div>
                </div>
            </div>

            <div class="filters-section">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>المندوب</label>
                        <select id="courier-filter">
                            <option value="">جميع المندوبين</option>
                            ${this.couriers.map(courier => 
                                `<option value="${courier.id}">${courier.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>نوع الراجع</label>
                        <select id="type-filter">
                            <option value="">جميع الأنواع</option>
                            <option value="full_return">راجع كلي</option>
                            <option value="partial_return">راجع جزئي</option>
                            <option value="delayed">مؤجل</option>
                            <option value="customer_issue">راجع بسبب الزبون</option>
                            <option value="courier_error">راجع بسبب خطأ مندوب</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>حالة الراجع</label>
                        <select id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="with_courier">في ذمة المندوب</option>
                            <option value="at_center">في مركز الشركة</option>
                            <option value="returned_to_company">تم إرجاعه للشركة</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="action-btn primary" onclick="returnsPage.applyFilters()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <button class="action-btn secondary" onclick="returnsPage.clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-container" id="returns-table-container">
                <!-- سيتم إدراج الجدول هنا -->
            </div>
        `;

        this.renderTable();
    }

    /**
     * حساب الإحصائيات
     * Calculate statistics
     */
    calculateStats() {
        const totalReturns = this.returns.length;
        const pendingReturns = this.returns.filter(r => !r.isReceived).length;
        const receivedReturns = this.returns.filter(r => r.isReceived).length;
        
        // حساب الرواجع المتأخرة (أكثر من 3 أيام)
        const threeDaysAgo = new Date();
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
        const overdueReturns = this.returns.filter(r => 
            !r.isReceived && new Date(r.returnDate) < threeDaysAgo
        ).length;

        return {
            totalReturns,
            pendingReturns,
            receivedReturns,
            overdueReturns
        };
    }

    /**
     * رسم الجدول
     * Render table
     */
    renderTable() {
        const tableContainer = document.getElementById('returns-table-container');
        if (!tableContainer) return;

        const columns = [
            { key: 'receiptNumber', title: 'رقم الوصل' },
            { key: 'courierName', title: 'المندوب' },
            { key: 'customerName', title: 'اسم الزبون' },
            { key: 'returnType', title: 'نوع الراجع', render: (value) => this.getReturnTypeText(value) },
            { key: 'returnDate', title: 'تاريخ الراجع', render: (value) => utils.formatDate(value, 'DD/MM/YYYY') },
            { key: 'returnStatus', title: 'حالة الراجع', render: (value) => this.renderStatusBadge(value) },
            { key: 'isReceived', title: 'مستلم', render: (value) => value ? '<span class="badge badge-success">نعم</span>' : '<span class="badge badge-warning">لا</span>' },
            { key: 'actions', title: 'الإجراءات', render: (value, row) => this.renderActions(row) }
        ];

        this.tableManager = new TableManager('returns-table-container', {
            columns: columns,
            searchable: false,
            sortable: true,
            paginated: true,
            pageSize: 20
        });

        this.prepareTableData();
    }

    /**
     * تحضير بيانات الجدول
     * Prepare table data
     */
    async prepareTableData() {
        const tableData = [];

        for (const returnRecord of this.returns) {
            // الحصول على تفاصيل الطلب
            const order = this.orders.find(o => o.id === returnRecord.orderId);
            
            // الحصول على اسم المندوب
            const courier = this.couriers.find(c => c.id === returnRecord.courierId);

            tableData.push({
                ...returnRecord,
                receiptNumber: order ? order.receiptNumber : 'غير محدد',
                customerName: order ? order.customerName : 'غير محدد',
                courierName: courier ? courier.name : 'غير محدد'
            });
        }

        this.tableManager.setData(tableData);
    }

    /**
     * الحصول على نص نوع الراجع
     * Get return type text
     */
    getReturnTypeText(type) {
        const types = {
            'full_return': 'راجع كلي',
            'partial_return': 'راجع جزئي',
            'delayed': 'مؤجل',
            'customer_issue': 'راجع بسبب الزبون',
            'courier_error': 'راجع بسبب خطأ مندوب'
        };
        return types[type] || type;
    }

    /**
     * رسم شارة الحالة
     * Render status badge
     */
    renderStatusBadge(status) {
        const statusConfig = {
            'with_courier': { text: 'في ذمة المندوب', class: 'warning' },
            'at_center': { text: 'في مركز الشركة', class: 'info' },
            'returned_to_company': { text: 'تم إرجاعه للشركة', class: 'success' }
        };

        const config = statusConfig[status] || { text: 'غير محدد', class: 'secondary' };
        return `<span class="badge badge-${config.class}">${config.text}</span>`;
    }

    /**
     * رسم أزرار الإجراءات
     * Render action buttons
     */
    renderActions(returnRecord) {
        let actions = `
            <div class="action-buttons">
                <button class="btn-sm btn-primary" onclick="returnsPage.viewReturn(${returnRecord.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
        `;

        if (!returnRecord.isReceived) {
            actions += `
                <button class="btn-sm btn-success" onclick="returnsPage.receiveReturn(${returnRecord.id})" title="استلام">
                    <i class="fas fa-hand-holding"></i>
                </button>
            `;
        }

        if (returnRecord.returnStatus === 'at_center') {
            actions += `
                <button class="btn-sm btn-info" onclick="returnsPage.transferToCompany(${returnRecord.id})" title="تحويل للشركة">
                    <i class="fas fa-share"></i>
                </button>
            `;
        }

        actions += `</div>`;
        return actions;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        ['courier-filter', 'type-filter', 'status-filter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', () => {
                    this.applyFilters();
                });
            }
        });
    }

    /**
     * تطبيق الفلاتر
     * Apply filters
     */
    applyFilters() {
        const courierId = document.getElementById('courier-filter')?.value || '';
        const returnType = document.getElementById('type-filter')?.value || '';
        const returnStatus = document.getElementById('status-filter')?.value || '';

        let filteredReturns = [...this.returns];

        if (courierId) {
            filteredReturns = filteredReturns.filter(r => 
                r.courierId === parseInt(courierId)
            );
        }

        if (returnType) {
            filteredReturns = filteredReturns.filter(r => 
                r.returnType === returnType
            );
        }

        if (returnStatus) {
            filteredReturns = filteredReturns.filter(r => 
                r.returnStatus === returnStatus
            );
        }

        this.returns = filteredReturns;
        this.prepareTableData();
    }

    /**
     * مسح الفلاتر
     * Clear filters
     */
    async clearFilters() {
        document.getElementById('courier-filter').value = '';
        document.getElementById('type-filter').value = '';
        document.getElementById('status-filter').value = '';

        await this.loadData();
        this.prepareTableData();
    }

    /**
     * عرض نافذة استلام الرواجع
     * Show receive returns modal
     */
    showReceiveReturnsModal() {
        const modalContent = `
            <form id="receive-returns-form">
                <div class="form-group">
                    <label class="form-label">أرقام الوصولات *</label>
                    <textarea name="receiptNumbers" class="form-textarea" rows="5" 
                              placeholder="أدخل أرقام الوصولات مفصولة بفواصل أو أسطر جديدة" required></textarea>
                    <small class="form-help">مثال: 12345, 12346, 12347</small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">المستلم *</label>
                    <input type="text" name="receivedBy" class="form-input" required 
                           value="${app.currentUser?.name || ''}" placeholder="اسم المستلم">
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" class="form-textarea" rows="3" 
                              placeholder="ملاحظات حول استلام الرواجع"></textarea>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="returnsPage.processReceiveReturns()">استلام الرواجع</button>
        `;

        modalManager.show('استلام رواجع', modalContent, { footer, maxWidth: '500px' });
    }

    /**
     * معالجة استلام الرواجع
     * Process receive returns
     */
    async processReceiveReturns() {
        try {
            const form = document.getElementById('receive-returns-form');
            const formData = new FormData(form);
            
            const receiptNumbers = formData.get('receiptNumbers')
                .split(/[,\n]/)
                .map(num => num.trim())
                .filter(num => num.length > 0);
            
            const receivedBy = formData.get('receivedBy').trim();
            const notes = formData.get('notes').trim();

            if (receiptNumbers.length === 0) {
                toastManager.show('يرجى إدخال أرقام الوصولات', 'warning');
                return;
            }

            if (!receivedBy) {
                toastManager.show('اسم المستلم مطلوب', 'warning');
                return;
            }

            const results = await returnsManager.receiveReturnsByReceipts(receiptNumbers, receivedBy, notes);
            
            modalManager.hide();
            
            if (results.success.length > 0) {
                toastManager.show(`تم استلام ${results.success.length} راجع بنجاح`, 'success');
            }
            
            if (results.errors.length > 0) {
                console.log('أخطاء في استلام الرواجع:', results.errors);
                toastManager.show(`${results.errors.length} خطأ في الاستلام`, 'warning');
            }

            await this.refresh();

        } catch (error) {
            console.error('خطأ في استلام الرواجع:', error);
            toastManager.show('حدث خطأ في استلام الرواجع', 'error');
        }
    }

    /**
     * عرض نافذة رواجع المندوبين
     * Show courier returns modal
     */
    async showCourierReturnsModal() {
        try {
            const report = await returnsManager.generateCouriersReturnsDebtReport();
            
            let modalContent = '<div class="courier-returns-report">';
            
            if (report.length === 0) {
                modalContent += '<p class="no-data">لا توجد رواجع معلقة</p>';
            } else {
                report.forEach(courierData => {
                    modalContent += `
                        <div class="courier-section">
                            <h4>${courierData.courier.name}</h4>
                            <p>عدد الرواجع المعلقة: <strong>${courierData.pendingReturns}</strong></p>
                            <p>إجمالي المبلغ: <strong>${utils.formatCurrency(courierData.totalAmount)}</strong></p>
                            
                            <div class="returns-list">
                                ${courierData.returns.map(returnItem => `
                                    <div class="return-item">
                                        <span>وصل ${returnItem.receiptNumber}</span>
                                        <span>${returnItem.customerName}</span>
                                        <span>${utils.formatCurrency(returnItem.amount)}</span>
                                        <span>${returnItem.daysPending} يوم</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                });
            }
            
            modalContent += '</div>';

            const footer = `
                <button class="action-btn secondary" onclick="modalManager.hide()">إغلاق</button>
                <button class="action-btn info" onclick="returnsPage.exportCourierReturns()">تصدير</button>
            `;

            modalManager.show('رواجع المندوبين', modalContent, { footer, maxWidth: '700px' });

        } catch (error) {
            console.error('خطأ في عرض رواجع المندوبين:', error);
            toastManager.show('حدث خطأ في عرض رواجع المندوبين', 'error');
        }
    }

    /**
     * عرض تفاصيل الراجع
     * View return details
     */
    viewReturn(returnId) {
        toastManager.show('سيتم إضافة عرض تفاصيل الراجع قريباً', 'info');
    }

    /**
     * استلام راجع واحد
     * Receive single return
     */
    async receiveReturn(returnId) {
        try {
            const receivedBy = app.currentUser?.name || 'موظف النظام';
            await returnsManager.receiveReturn(returnId, receivedBy, 'تم الاستلام');
            
            toastManager.show('تم استلام الراجع بنجاح', 'success');
            await this.refresh();

        } catch (error) {
            console.error('خطأ في استلام الراجع:', error);
            toastManager.show('حدث خطأ في استلام الراجع', 'error');
        }
    }

    /**
     * تحويل راجع للشركة
     * Transfer return to company
     */
    async transferToCompany(returnId) {
        try {
            const transferredBy = app.currentUser?.name || 'موظف النظام';
            await returnsManager.transferReturnsToCenter([returnId], transferredBy, 'تم التحويل للشركة');
            
            toastManager.show('تم تحويل الراجع للشركة بنجاح', 'success');
            await this.refresh();

        } catch (error) {
            console.error('خطأ في تحويل الراجع:', error);
            toastManager.show('حدث خطأ في تحويل الراجع', 'error');
        }
    }

    /**
     * تصدير الرواجع
     * Export returns
     */
    exportReturns() {
        toastManager.show('سيتم إضافة تصدير الرواجع قريباً', 'info');
    }

    /**
     * تصدير رواجع المندوبين
     * Export courier returns
     */
    exportCourierReturns() {
        toastManager.show('سيتم إضافة تصدير رواجع المندوبين قريباً', 'info');
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadData();
        this.render();
    }
}

// إنشاء مثيل الصفحة
const returnsPage = new ReturnsPage();

// تصدير الصفحة
window.ReturnsPage = ReturnsPage;
window.returnsPage = returnsPage;
