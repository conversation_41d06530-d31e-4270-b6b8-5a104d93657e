/**
 * مدير البيانات - وظائف متقدمة لإدارة البيانات
 * Data Manager - Advanced data management functions
 */

class DataManager {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
    }

    /**
     * إدارة المندوبين
     * Couriers Management
     */
    async addCourier(courierData) {
        try {
            // التحقق من صحة البيانات
            const validation = this.validateCourierData(courierData);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // التحقق من عدم تكرار رقم الهاتف
            const existingCourier = await db.getByIndex('couriers', 'phone', courierData.phone);
            if (existingCourier.length > 0) {
                throw new Error('رقم الهاتف موجود مسبقاً');
            }

            // إضافة المندوب
            const courierId = await db.add('couriers', {
                ...courierData,
                isActive: true,
                totalOrders: 0,
                completedOrders: 0,
                totalCommission: 0,
                createdDate: new Date()
            });

            this.clearCache('couriers');
            return courierId;

        } catch (error) {
            console.error('خطأ في إضافة المندوب:', error);
            throw error;
        }
    }

    /**
     * تحديث مندوب
     * Update courier
     */
    async updateCourier(courierId, courierData) {
        try {
            const existingCourier = await db.getById('couriers', courierId);
            if (!existingCourier) {
                throw new Error('المندوب غير موجود');
            }

            // التحقق من صحة البيانات
            const validation = this.validateCourierData(courierData);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // التحقق من عدم تكرار رقم الهاتف (إذا تم تغييره)
            if (courierData.phone !== existingCourier.phone) {
                const duplicatePhone = await db.getByIndex('couriers', 'phone', courierData.phone);
                if (duplicatePhone.length > 0) {
                    throw new Error('رقم الهاتف موجود مسبقاً');
                }
            }

            // تحديث المندوب
            await db.update('couriers', {
                ...existingCourier,
                ...courierData,
                updatedDate: new Date()
            });

            this.clearCache('couriers');
            return true;

        } catch (error) {
            console.error('خطأ في تحديث المندوب:', error);
            throw error;
        }
    }

    /**
     * التحقق من صحة بيانات المندوب
     * Validate courier data
     */
    validateCourierData(data) {
        const errors = [];

        if (!data.name || data.name.trim().length < 2) {
            errors.push('اسم المندوب مطلوب ويجب أن يكون أكثر من حرفين');
        }

        if (!data.phone || !utils.validatePhone(data.phone)) {
            errors.push('رقم هاتف صحيح مطلوب');
        }

        if (!data.region || data.region.trim().length === 0) {
            errors.push('المنطقة مطلوبة');
        }

        if (data.commission !== undefined && (isNaN(data.commission) || data.commission < 0)) {
            errors.push('العمولة يجب أن تكون رقم موجب');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * إدارة الشركات
     * Companies Management
     */
    async addCompany(companyData) {
        try {
            // التحقق من صحة البيانات
            const validation = this.validateCompanyData(companyData);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // التحقق من عدم تكرار اسم الشركة
            const existingCompany = await db.getByIndex('companies', 'name', companyData.name);
            if (existingCompany.length > 0) {
                throw new Error('اسم الشركة موجود مسبقاً');
            }

            // إضافة الشركة
            const companyId = await db.add('companies', {
                ...companyData,
                isActive: true,
                totalOrders: 0,
                totalAmount: 0,
                createdDate: new Date()
            });

            this.clearCache('companies');
            return companyId;

        } catch (error) {
            console.error('خطأ في إضافة الشركة:', error);
            throw error;
        }
    }

    /**
     * التحقق من صحة بيانات الشركة
     * Validate company data
     */
    validateCompanyData(data) {
        const errors = [];

        if (!data.name || data.name.trim().length < 2) {
            errors.push('اسم الشركة مطلوب ويجب أن يكون أكثر من حرفين');
        }

        if (data.phone && !utils.validatePhone(data.phone)) {
            errors.push('رقم الهاتف غير صحيح');
        }

        if (data.email && !utils.validateEmail(data.email)) {
            errors.push('البريد الإلكتروني غير صحيح');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * إدارة الطلبات
     * Orders Management
     */
    async addOrder(orderData) {
        try {
            // التحقق من صحة البيانات
            const validation = this.validateOrderData(orderData);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // التحقق من عدم تكرار رقم الوصل
            const existingOrder = await db.getByIndex('orders', 'receiptNumber', orderData.receiptNumber);
            if (existingOrder.length > 0) {
                throw new Error('رقم الوصل موجود مسبقاً');
            }

            // إضافة الطلب
            const orderId = await db.add('orders', {
                ...orderData,
                status: orderData.status || 'pending',
                isArchived: false,
                createdDate: new Date()
            });

            // تحديث إحصائيات الشركة
            if (orderData.companyId) {
                await this.updateCompanyStats(orderData.companyId);
            }

            // تحديث إحصائيات المندوب
            if (orderData.courierId) {
                await this.updateCourierStats(orderData.courierId);
            }

            this.clearCache('orders');
            return orderId;

        } catch (error) {
            console.error('خطأ في إضافة الطلب:', error);
            throw error;
        }
    }

    /**
     * تحديث حالة الطلب
     * Update order status
     */
    async updateOrderStatus(orderId, newStatus, notes = '') {
        try {
            const order = await db.getById('orders', orderId);
            if (!order) {
                throw new Error('الطلب غير موجود');
            }

            const oldStatus = order.status;

            // تحديث الطلب
            await db.update('orders', {
                ...order,
                status: newStatus,
                statusNotes: notes,
                statusUpdatedDate: new Date(),
                updatedDate: new Date()
            });

            // إضافة سجل في تاريخ الطلب
            await this.addOrderHistory(orderId, {
                action: 'status_change',
                oldStatus,
                newStatus,
                notes,
                date: new Date(),
                userId: app.currentUser?.id
            });

            // تحديث الإحصائيات إذا تغيرت الحالة
            if (oldStatus !== newStatus) {
                if (order.companyId) {
                    await this.updateCompanyStats(order.companyId);
                }
                if (order.courierId) {
                    await this.updateCourierStats(order.courierId);
                }
            }

            this.clearCache('orders');
            return true;

        } catch (error) {
            console.error('خطأ في تحديث حالة الطلب:', error);
            throw error;
        }
    }

    /**
     * إسناد طلبات إلى مندوب
     * Assign orders to courier
     */
    async assignOrdersToCourier(receiptNumbers, courierId) {
        try {
            const courier = await db.getById('couriers', courierId);
            if (!courier) {
                throw new Error('المندوب غير موجود');
            }

            const assignedOrders = [];
            const errors = [];

            for (const receiptNumber of receiptNumbers) {
                try {
                    const orders = await db.getByIndex('orders', 'receiptNumber', receiptNumber.trim());
                    if (orders.length === 0) {
                        errors.push(`الوصل رقم ${receiptNumber} غير موجود`);
                        continue;
                    }

                    const order = orders[0];
                    
                    // تحديث الطلب
                    await db.update('orders', {
                        ...order,
                        courierId: courierId,
                        assignedDate: new Date(),
                        updatedDate: new Date()
                    });

                    // إضافة سجل في تاريخ الطلب
                    await this.addOrderHistory(order.id, {
                        action: 'assigned',
                        courierId: courierId,
                        courierName: courier.name,
                        date: new Date(),
                        userId: app.currentUser?.id
                    });

                    assignedOrders.push(order);

                } catch (error) {
                    errors.push(`خطأ في إسناد الوصل رقم ${receiptNumber}: ${error.message}`);
                }
            }

            // تحديث إحصائيات المندوب
            if (assignedOrders.length > 0) {
                await this.updateCourierStats(courierId);
            }

            this.clearCache('orders');

            return {
                success: assignedOrders.length,
                errors: errors
            };

        } catch (error) {
            console.error('خطأ في إسناد الطلبات:', error);
            throw error;
        }
    }

    /**
     * التحقق من صحة بيانات الطلب
     * Validate order data
     */
    validateOrderData(data) {
        const errors = [];

        if (!data.receiptNumber || data.receiptNumber.trim().length === 0) {
            errors.push('رقم الوصل مطلوب');
        }

        if (!data.companyId) {
            errors.push('الشركة مطلوبة');
        }

        if (!data.customerName || data.customerName.trim().length < 2) {
            errors.push('اسم الزبون مطلوب');
        }

        if (!data.customerPhone || !utils.validatePhone(data.customerPhone)) {
            errors.push('رقم هاتف الزبون مطلوب وصحيح');
        }

        if (!data.address || data.address.trim().length < 5) {
            errors.push('العنوان مطلوب ويجب أن يكون مفصلاً');
        }

        if (!data.amount || isNaN(data.amount) || data.amount <= 0) {
            errors.push('المبلغ مطلوب ويجب أن يكون أكبر من صفر');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * إضافة سجل في تاريخ الطلب
     * Add order history record
     */
    async addOrderHistory(orderId, historyData) {
        try {
            await db.add('order_history', {
                orderId: orderId,
                ...historyData,
                createdDate: new Date()
            });
        } catch (error) {
            console.error('خطأ في إضافة سجل التاريخ:', error);
        }
    }

    /**
     * تحديث إحصائيات الشركة
     * Update company statistics
     */
    async updateCompanyStats(companyId) {
        try {
            const company = await db.getById('companies', companyId);
            if (!company) return;

            const companyOrders = await db.getByIndex('orders', 'companyId', companyId);
            
            const stats = {
                totalOrders: companyOrders.length,
                totalAmount: companyOrders.reduce((sum, order) => sum + (order.amount || 0), 0),
                deliveredOrders: companyOrders.filter(order => order.status === 'delivered').length,
                pendingOrders: companyOrders.filter(order => order.status === 'pending').length,
                returnedOrders: companyOrders.filter(order => order.status === 'returned').length
            };

            await db.update('companies', {
                ...company,
                ...stats,
                updatedDate: new Date()
            });

        } catch (error) {
            console.error('خطأ في تحديث إحصائيات الشركة:', error);
        }
    }

    /**
     * تحديث إحصائيات المندوب
     * Update courier statistics
     */
    async updateCourierStats(courierId) {
        try {
            const courier = await db.getById('couriers', courierId);
            if (!courier) return;

            const courierOrders = await db.getByIndex('orders', 'courierId', courierId);
            
            const completedOrders = courierOrders.filter(order => order.status === 'delivered');
            const totalCommission = completedOrders.reduce((sum, order) => {
                return sum + ((order.amount || 0) * (courier.commission || 0) / 100);
            }, 0);

            const stats = {
                totalOrders: courierOrders.length,
                completedOrders: completedOrders.length,
                totalCommission: totalCommission,
                successRate: courierOrders.length > 0 ? (completedOrders.length / courierOrders.length * 100) : 0
            };

            await db.update('couriers', {
                ...courier,
                ...stats,
                updatedDate: new Date()
            });

        } catch (error) {
            console.error('خطأ في تحديث إحصائيات المندوب:', error);
        }
    }

    /**
     * إدارة الكاش
     * Cache management
     */
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    getCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        // التحقق من انتهاء صلاحية الكاش
        if (Date.now() - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }

        return cached.data;
    }

    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }

    /**
     * البحث المتقدم في الطلبات
     * Advanced order search
     */
    async searchOrders(searchParams) {
        try {
            const cacheKey = `search_${JSON.stringify(searchParams)}`;
            const cached = this.getCache(cacheKey);
            if (cached) return cached;

            let orders = await db.getAll('orders');

            // تطبيق الفلاتر
            if (searchParams.receiptNumber) {
                orders = orders.filter(order => 
                    order.receiptNumber.includes(searchParams.receiptNumber)
                );
            }

            if (searchParams.customerName) {
                orders = orders.filter(order => 
                    utils.searchInText(order.customerName, searchParams.customerName)
                );
            }

            if (searchParams.customerPhone) {
                orders = orders.filter(order => 
                    order.customerPhone.includes(searchParams.customerPhone)
                );
            }

            if (searchParams.companyId) {
                orders = orders.filter(order => 
                    order.companyId === parseInt(searchParams.companyId)
                );
            }

            if (searchParams.courierId) {
                orders = orders.filter(order => 
                    order.courierId === parseInt(searchParams.courierId)
                );
            }

            if (searchParams.status) {
                orders = orders.filter(order => 
                    order.status === searchParams.status
                );
            }

            if (searchParams.dateFrom) {
                const fromDate = new Date(searchParams.dateFrom);
                orders = orders.filter(order => 
                    new Date(order.createdDate) >= fromDate
                );
            }

            if (searchParams.dateTo) {
                const toDate = new Date(searchParams.dateTo);
                toDate.setHours(23, 59, 59, 999);
                orders = orders.filter(order => 
                    new Date(order.createdDate) <= toDate
                );
            }

            // ترتيب النتائج
            orders = utils.sortArray(orders, 'createdDate', 'desc');

            this.setCache(cacheKey, orders);
            return orders;

        } catch (error) {
            console.error('خطأ في البحث:', error);
            throw error;
        }
    }

    /**
     * إنشاء بيانات تجريبية عراقية
     * Create Iraqi sample data
     */
    async createIraqiSampleData() {
        try {
            // التحقق من وجود بيانات
            const existingCompanies = await db.getAll('companies');
            const existingCouriers = await db.getAll('couriers');

            if (existingCompanies.length === 0) {
                // إضافة شركات عراقية تجريبية
                const sampleCompanies = [
                    {
                        name: 'شركة الأزياء العراقية',
                        phone: '07701234567',
                        email: '<EMAIL>',
                        address: 'بغداد، منطقة الكرادة',
                        isActive: true,
                        createdDate: new Date('2024-01-15')
                    },
                    {
                        name: 'متجر الإلكترونيات المتقدمة',
                        phone: '07809876543',
                        email: '<EMAIL>',
                        address: 'البصرة، منطقة العشار',
                        isActive: true,
                        createdDate: new Date('2024-02-01')
                    },
                    {
                        name: 'مطعم بغداد الأصيل',
                        phone: '07751122334',
                        email: '<EMAIL>',
                        address: 'أربيل، منطقة عنكاوا',
                        isActive: true,
                        createdDate: new Date('2024-02-10')
                    }
                ];

                for (const company of sampleCompanies) {
                    await db.add('companies', company);
                }
            }

            if (existingCouriers.length === 0) {
                // إضافة مندوبين عراقيين تجريبيين
                const sampleCouriers = [
                    {
                        name: 'أحمد محمد علي',
                        phone: '07701111111',
                        region: 'بغداد',
                        commission: 5,
                        isActive: true,
                        totalOrders: 0,
                        completedOrders: 0,
                        totalCommission: 0,
                        createdDate: new Date('2024-01-20')
                    },
                    {
                        name: 'فاطمة حسن الزهراء',
                        phone: '07802222222',
                        region: 'البصرة',
                        commission: 6,
                        isActive: true,
                        totalOrders: 0,
                        completedOrders: 0,
                        totalCommission: 0,
                        createdDate: new Date('2024-02-05')
                    },
                    {
                        name: 'عمر خالد الكردي',
                        phone: '07753333333',
                        region: 'أربيل',
                        commission: 5.5,
                        isActive: true,
                        totalOrders: 0,
                        completedOrders: 0,
                        totalCommission: 0,
                        createdDate: new Date('2024-02-15')
                    }
                ];

                for (const courier of sampleCouriers) {
                    await db.add('couriers', courier);
                }
            }

            // إضافة مناطق عراقية
            const existingRegions = await db.getAll('regions');
            if (existingRegions.length === 0) {
                const iraqiRegions = [
                    { name: 'بغداد', description: 'العاصمة العراقية', isActive: true, createdDate: new Date() },
                    { name: 'البصرة', description: 'محافظة البصرة', isActive: true, createdDate: new Date() },
                    { name: 'أربيل', description: 'عاصمة إقليم كردستان', isActive: true, createdDate: new Date() },
                    { name: 'الموصل', description: 'محافظة نينوى', isActive: true, createdDate: new Date() },
                    { name: 'النجف', description: 'المدينة المقدسة', isActive: true, createdDate: new Date() },
                    { name: 'كربلاء', description: 'المدينة المقدسة', isActive: true, createdDate: new Date() }
                ];

                for (const region of iraqiRegions) {
                    await db.add('regions', region);
                }
            }

            console.log('تم إنشاء البيانات التجريبية العراقية بنجاح');

        } catch (error) {
            console.error('خطأ في إنشاء البيانات التجريبية:', error);
        }
    }

    /**
     * مسح الذاكرة المؤقتة
     * Clear cache
     */
    clearCache(type = null) {
        if (type) {
            // مسح نوع معين من الذاكرة المؤقتة
            for (const [key, value] of this.cache.entries()) {
                if (key.startsWith(type)) {
                    this.cache.delete(key);
                }
            }
        } else {
            // مسح جميع الذاكرة المؤقتة
            this.cache.clear();
        }
    }
}

// إنشاء مثيل مدير البيانات
const dataManager = new DataManager();

// تصدير مدير البيانات
window.dataManager = dataManager;
window.DataManager = DataManager;
