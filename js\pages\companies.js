/**
 * صفحة إدارة الشركات
 * Companies Management Page
 */

class CompaniesPage {
    constructor() {
        this.container = document.getElementById('companies-page');
        this.tableManager = null;
        this.companies = [];
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة الشركات:', error);
            toastManager.show('حدث خطأ في تحميل صفحة الشركات', 'error');
        }
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            this.companies = await db.getAll('companies');
        } catch (error) {
            console.error('خطأ في تحميل بيانات الشركات:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>إدارة الشركات</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="companiesPage.showAddCompanyModal()">
                        <i class="fas fa-plus"></i>
                        شركة جديدة
                    </button>
                    <button class="action-btn info" onclick="companiesPage.exportCompanies()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.companies.length}</h3>
                        <p>إجمالي الشركات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.companies.filter(c => c.isActive).length}</h3>
                        <p>شركات نشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.companies.filter(c => !c.isActive).length}</h3>
                        <p>شركات غير نشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.companies.reduce((sum, c) => sum + (c.totalOrders || 0), 0)}</h3>
                        <p>إجمالي الطلبات</p>
                    </div>
                </div>
            </div>

            <div class="table-container" id="companies-table-container">
                <!-- سيتم إدراج الجدول هنا -->
            </div>
        `;

        this.renderTable();
    }

    /**
     * رسم الجدول
     * Render table
     */
    renderTable() {
        const tableContainer = document.getElementById('companies-table-container');
        if (!tableContainer) return;

        const columns = [
            { key: 'name', title: 'اسم الشركة' },
            { key: 'phone', title: 'رقم الهاتف', render: (value) => value ? utils.formatPhone(value) : '-' },
            { key: 'email', title: 'البريد الإلكتروني', render: (value) => value || '-' },
            { key: 'totalOrders', title: 'عدد الطلبات', render: (value) => value || 0 },
            { key: 'totalAmount', title: 'إجمالي المبلغ', render: (value) => utils.formatCurrency(value || 0) },
            { key: 'isActive', title: 'الحالة', render: (value) => this.renderStatusBadge(value) },
            { key: 'createdDate', title: 'تاريخ الإضافة', render: (value) => utils.formatDate(value, 'DD/MM/YYYY') },
            { key: 'actions', title: 'الإجراءات', render: (value, row) => this.renderActions(row) }
        ];

        this.tableManager = new TableManager('companies-table-container', {
            columns: columns,
            searchable: true,
            sortable: true,
            paginated: true,
            pageSize: 15
        });

        this.tableManager.setData(this.companies);
    }

    /**
     * رسم شارة الحالة
     * Render status badge
     */
    renderStatusBadge(isActive) {
        if (isActive) {
            return '<span class="badge badge-success">نشطة</span>';
        } else {
            return '<span class="badge badge-danger">غير نشطة</span>';
        }
    }

    /**
     * رسم أزرار الإجراءات
     * Render action buttons
     */
    renderActions(company) {
        return `
            <div class="action-buttons">
                <button class="btn-sm btn-primary" onclick="companiesPage.viewCompany(${company.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-sm btn-secondary" onclick="companiesPage.editCompany(${company.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-sm btn-info" onclick="companiesPage.viewOrders(${company.id})" title="الطلبات">
                    <i class="fas fa-box"></i>
                </button>
                <button class="btn-sm btn-warning" onclick="companiesPage.toggleStatus(${company.id})" title="تغيير الحالة">
                    <i class="fas fa-toggle-on"></i>
                </button>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // لا توجد أحداث إضافية حالياً
    }

    /**
     * عرض نافذة إضافة شركة جديدة
     * Show add company modal
     */
    showAddCompanyModal() {
        const modalContent = `
            <form id="add-company-form">
                <div class="form-group">
                    <label class="form-label">اسم الشركة *</label>
                    <input type="text" name="name" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" name="phone" class="form-input" placeholder="011xxxxxxx">
                </div>
                
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" name="email" class="form-input" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label class="form-label">العنوان</label>
                    <textarea name="address" class="form-textarea" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" class="form-textarea" rows="2"></textarea>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="companiesPage.saveCompany()">حفظ الشركة</button>
        `;

        modalManager.show('شركة جديدة', modalContent, { footer, maxWidth: '500px' });
    }

    /**
     * حفظ شركة جديدة
     * Save new company
     */
    async saveCompany() {
        try {
            const form = document.getElementById('add-company-form');
            const formData = new FormData(form);
            
            const companyData = {
                name: formData.get('name').trim(),
                phone: formData.get('phone').trim(),
                email: formData.get('email').trim(),
                address: formData.get('address').trim(),
                notes: formData.get('notes').trim()
            };

            if (!companyData.name || companyData.name.length < 2) {
                toastManager.show('اسم الشركة مطلوب ويجب أن يكون أكثر من حرفين', 'warning');
                return;
            }

            if (companyData.phone && !utils.validatePhone(companyData.phone)) {
                toastManager.show('رقم الهاتف غير صحيح', 'warning');
                return;
            }

            if (companyData.email && !utils.validateEmail(companyData.email)) {
                toastManager.show('البريد الإلكتروني غير صحيح', 'warning');
                return;
            }

            await dataManager.addCompany(companyData);
            
            modalManager.hide();
            toastManager.show('تم إضافة الشركة بنجاح', 'success');
            
            await this.refresh();

        } catch (error) {
            console.error('خطأ في حفظ الشركة:', error);
            toastManager.show(error.message || 'حدث خطأ في حفظ الشركة', 'error');
        }
    }

    /**
     * عرض تفاصيل الشركة
     * View company details
     */
    async viewCompany(companyId) {
        try {
            const company = await db.getById('companies', companyId);
            if (!company) {
                toastManager.show('لم يتم العثور على الشركة', 'error');
                return;
            }

            const orders = await db.getByIndex('orders', 'companyId', companyId);
            const profits = await accountingManager.calculateCompanyProfits(companyId);

            const modalContent = `
                <div class="company-details">
                    <div class="detail-section">
                        <h4>المعلومات الأساسية</h4>
                        <div class="detail-row">
                            <strong>اسم الشركة:</strong>
                            <span>${company.name}</span>
                        </div>
                        ${company.phone ? `
                            <div class="detail-row">
                                <strong>رقم الهاتف:</strong>
                                <span>${utils.formatPhone(company.phone)}</span>
                            </div>
                        ` : ''}
                        ${company.email ? `
                            <div class="detail-row">
                                <strong>البريد الإلكتروني:</strong>
                                <span>${company.email}</span>
                            </div>
                        ` : ''}
                        ${company.address ? `
                            <div class="detail-row">
                                <strong>العنوان:</strong>
                                <span>${company.address}</span>
                            </div>
                        ` : ''}
                        <div class="detail-row">
                            <strong>الحالة:</strong>
                            <span class="badge badge-${company.isActive ? 'success' : 'danger'}">
                                ${company.isActive ? 'نشطة' : 'غير نشطة'}
                            </span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>الإحصائيات</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">إجمالي الطلبات</span>
                                <span class="stat-value">${orders.length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">طلبات مسلمة</span>
                                <span class="stat-value">${orders.filter(o => o.status === 'delivered').length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">إجمالي الإيرادات</span>
                                <span class="stat-value">${utils.formatCurrency(profits.totalRevenue)}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">إجمالي الأرباح</span>
                                <span class="stat-value">${utils.formatCurrency(profits.totalProfit)}</span>
                            </div>
                        </div>
                    </div>

                    ${company.notes ? `
                        <div class="detail-section">
                            <h4>ملاحظات</h4>
                            <p>${company.notes}</p>
                        </div>
                    ` : ''}
                </div>
            `;

            const footer = `
                <button class="action-btn secondary" onclick="modalManager.hide()">إغلاق</button>
                <button class="action-btn primary" onclick="modalManager.hide(); companiesPage.editCompany(${companyId})">تعديل</button>
            `;

            modalManager.show('تفاصيل الشركة', modalContent, { footer, maxWidth: '600px' });

        } catch (error) {
            console.error('خطأ في عرض تفاصيل الشركة:', error);
            toastManager.show('حدث خطأ في عرض تفاصيل الشركة', 'error');
        }
    }

    /**
     * تعديل الشركة
     * Edit company
     */
    editCompany(companyId) {
        toastManager.show('سيتم إضافة تعديل الشركات قريباً', 'info');
    }

    /**
     * عرض طلبات الشركة
     * View company orders
     */
    viewOrders(companyId) {
        navigateToPage('orders');
    }

    /**
     * تغيير حالة الشركة
     * Toggle company status
     */
    async toggleStatus(companyId) {
        try {
            const company = await db.getById('companies', companyId);
            if (!company) {
                toastManager.show('لم يتم العثور على الشركة', 'error');
                return;
            }

            await db.update('companies', {
                ...company,
                isActive: !company.isActive,
                updatedDate: new Date()
            });

            toastManager.show(`تم ${company.isActive ? 'إلغاء تفعيل' : 'تفعيل'} الشركة بنجاح`, 'success');
            await this.refresh();

        } catch (error) {
            console.error('خطأ في تغيير حالة الشركة:', error);
            toastManager.show('حدث خطأ في تغيير حالة الشركة', 'error');
        }
    }

    /**
     * تصدير الشركات
     * Export companies
     */
    exportCompanies() {
        try {
            const exportData = this.companies.map(company => ({
                'اسم الشركة': company.name,
                'رقم الهاتف': company.phone || '',
                'البريد الإلكتروني': company.email || '',
                'العنوان': company.address || '',
                'عدد الطلبات': company.totalOrders || 0,
                'إجمالي المبلغ': company.totalAmount || 0,
                'الحالة': company.isActive ? 'نشطة' : 'غير نشطة',
                'تاريخ الإضافة': utils.formatDate(company.createdDate, 'DD/MM/YYYY'),
                'ملاحظات': company.notes || ''
            }));

            utils.exportToCSV(exportData, `companies_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
            toastManager.show('تم تصدير الشركات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير الشركات:', error);
            toastManager.show('حدث خطأ في تصدير الشركات', 'error');
        }
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadData();
        this.render();
    }
}

// إنشاء مثيل الصفحة
const companiesPage = new CompaniesPage();

// تصدير الصفحة
window.CompaniesPage = CompaniesPage;
window.companiesPage = companiesPage;
