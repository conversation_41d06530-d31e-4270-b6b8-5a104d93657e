/* ===== Page Content ===== */
.page-content {
    flex: 1;
    padding: var(--spacing-6);
    background: var(--bg-primary);
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Dashboard Components ===== */
.dashboard-grid {
    display: grid;
    gap: var(--spacing-6);
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: var(--white);
    font-size: var(--font-size-xl);
    box-shadow: var(--neu-shadow-outset);
}

.stat-icon.success {
    background: var(--success-color);
}

.stat-icon.warning {
    background: var(--warning-color);
}

.stat-icon.danger {
    background: var(--danger-color);
}

.stat-icon.info {
    background: var(--info-color);
}

.stat-info h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.stat-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* ===== Dashboard Content ===== */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
}

.dashboard-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
}

.dashboard-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--gray-200);
}

/* ===== Recent Orders ===== */
.recent-orders {
    max-height: 400px;
    overflow-y: auto;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-2);
    background: var(--white);
    box-shadow: var(--neu-shadow-inset);
    transition: all var(--transition-fast);
}

.order-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-md);
}

.order-info {
    flex: 1;
}

.order-number {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.order-details {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.order-status {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.delivered {
    background: var(--success-color);
    color: var(--white);
}

.order-status.pending {
    background: var(--warning-color);
    color: var(--white);
}

.order-status.returned {
    background: var(--danger-color);
    color: var(--white);
}

/* ===== Quick Actions ===== */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--neu-shadow-outset);
    text-decoration: none;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-pressed);
}

.action-btn.primary {
    background: var(--primary-color);
    color: var(--white);
}

.action-btn.secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.action-btn.info {
    background: var(--info-color);
    color: var(--white);
}

.action-btn.success {
    background: var(--success-color);
    color: var(--white);
}

.action-btn.warning {
    background: var(--warning-color);
    color: var(--white);
}

.action-btn.danger {
    background: var(--danger-color);
    color: var(--white);
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: var(--spacing-4);
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    background: var(--white);
    color: var(--gray-700);
    transition: all var(--transition-fast);
    box-shadow: var(--neu-shadow-inset);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* ===== Tables ===== */
.table-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-4);
}

.table th,
.table td {
    padding: var(--spacing-3);
    text-align: right;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-800);
    position: sticky;
    top: 0;
}

.table tr:hover {
    background: var(--gray-50);
}

/* ===== Modals ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: all var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-6);
}

.modal-footer {
    padding: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
}

/* ===== Toast Notifications ===== */
.toast-container {
    position: fixed;
    top: var(--spacing-6);
    left: var(--spacing-6);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.toast {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    min-width: 300px;
    transform: translateX(-100%);
    transition: all var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-right: 4px solid var(--success-color);
}

.toast.error {
    border-right: 4px solid var(--danger-color);
}

.toast.warning {
    border-right: 4px solid var(--warning-color);
}

.toast.info {
    border-right: 4px solid var(--info-color);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* ===== Utility Classes ===== */
.no-data {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: var(--spacing-8);
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}
