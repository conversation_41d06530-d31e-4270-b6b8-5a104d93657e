/* ===== Page Content ===== */
.page-content {
    flex: 1;
    padding: var(--spacing-6);
    background: var(--bg-primary);
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Dashboard Components ===== */
.dashboard-grid {
    display: grid;
    gap: var(--spacing-6);
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: var(--white);
    font-size: var(--font-size-xl);
    box-shadow: var(--neu-shadow-outset);
}

.stat-icon.success {
    background: var(--success-color);
}

.stat-icon.warning {
    background: var(--warning-color);
}

.stat-icon.danger {
    background: var(--danger-color);
}

.stat-icon.info {
    background: var(--info-color);
}

.stat-info h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.stat-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* ===== Dashboard Content ===== */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
}

.dashboard-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
}

.dashboard-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--gray-200);
}

/* ===== Recent Orders ===== */
.recent-orders {
    max-height: 400px;
    overflow-y: auto;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-2);
    background: var(--white);
    box-shadow: var(--neu-shadow-inset);
    transition: all var(--transition-fast);
}

.order-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-md);
}

.order-info {
    flex: 1;
}

.order-number {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.order-details {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.order-status {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.delivered {
    background: var(--success-color);
    color: var(--white);
}

.order-status.pending {
    background: var(--warning-color);
    color: var(--white);
}

.order-status.returned {
    background: var(--danger-color);
    color: var(--white);
}

/* ===== Quick Actions ===== */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--neu-shadow-outset);
    text-decoration: none;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: var(--neu-shadow-pressed);
}

.action-btn.primary {
    background: var(--primary-color);
    color: var(--white);
}

.action-btn.secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.action-btn.info {
    background: var(--info-color);
    color: var(--white);
}

.action-btn.success {
    background: var(--success-color);
    color: var(--white);
}

.action-btn.warning {
    background: var(--warning-color);
    color: var(--white);
}

.action-btn.danger {
    background: var(--danger-color);
    color: var(--white);
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: var(--spacing-4);
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    background: var(--white);
    color: var(--gray-700);
    transition: all var(--transition-fast);
    box-shadow: var(--neu-shadow-inset);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* ===== Tables ===== */
.table-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-4);
}

.table th,
.table td {
    padding: var(--spacing-3);
    text-align: right;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-800);
    position: sticky;
    top: 0;
}

.table tr:hover {
    background: var(--gray-50);
}

/* ===== Modals ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: all var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-6);
}

.modal-footer {
    padding: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
}

/* ===== Toast Notifications ===== */
.toast-container {
    position: fixed;
    top: var(--spacing-6);
    left: var(--spacing-6);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.toast {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    min-width: 300px;
    transform: translateX(-100%);
    transition: all var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-right: 4px solid var(--success-color);
}

.toast.error {
    border-right: 4px solid var(--danger-color);
}

.toast.warning {
    border-right: 4px solid var(--warning-color);
}

.toast.info {
    border-right: 4px solid var(--info-color);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* ===== Page Layout ===== */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 2px solid var(--gray-200);
}

.page-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.page-actions {
    display: flex;
    gap: var(--spacing-3);
}

/* ===== Filters Section ===== */
.filters-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    box-shadow: var(--neu-shadow-outset);
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

/* ===== Badges ===== */
.badge {
    display: inline-block;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-color);
    color: var(--white);
}

.badge-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.badge-success {
    background: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--white);
}

.badge-danger {
    background: var(--danger-color);
    color: var(--white);
}

.badge-info {
    background: var(--info-color);
    color: var(--white);
}

/* ===== Action Buttons ===== */
.action-buttons {
    display: flex;
    gap: var(--spacing-2);
}

.btn-sm {
    padding: var(--spacing-1) var(--spacing-2);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: var(--gray-600);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-info {
    background: var(--info-color);
    color: var(--white);
}

/* ===== Detail Sections ===== */
.detail-section {
    margin-bottom: var(--spacing-6);
}

.detail-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--gray-200);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row strong {
    color: var(--gray-700);
    font-weight: 600;
}

/* ===== Stats Grid ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-4);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.stat-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
}

.stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== Form Error States ===== */
.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-error {
    color: var(--danger-color);
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-1);
    display: block;
}

/* ===== Utility Classes ===== */
.no-data {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: var(--spacing-8);
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* ===== Coming Soon Styles ===== */
.coming-soon {
    text-align: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

.coming-soon i {
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.coming-soon h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-3);
    color: var(--gray-700);
}

.coming-soon p {
    font-size: var(--font-size-lg);
}

/* ===== Return Styles ===== */
.courier-returns-report {
    max-height: 500px;
    overflow-y: auto;
}

.courier-section {
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.courier-section h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-3);
}

.returns-list {
    margin-top: var(--spacing-3);
}

.return-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: var(--spacing-3);
    padding: var(--spacing-2);
    background: var(--white);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.return-item:last-child {
    margin-bottom: 0;
}

/* ===== Form Help Text ===== */
.form-help {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-1);
    display: block;
}

/* ===== Accounting Styles ===== */
.financial-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.financial-stats .stat-card {
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow-outset);
    background: var(--bg-secondary);
}

.financial-stats .stat-card.revenue {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.financial-stats .stat-card.expenses {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
}

.financial-stats .stat-card.profit {
    background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 100%);
}

.financial-stats .stat-card.average {
    background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
}

.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.chart-container {
    background: var(--bg-secondary);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow-outset);
}

.chart-container h4 {
    margin-bottom: var(--spacing-4);
    color: var(--gray-800);
    font-weight: 600;
}

.simple-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.chart-item {
    display: grid;
    grid-template-columns: 120px 1fr 80px;
    gap: var(--spacing-3);
    align-items: center;
}

.chart-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.chart-bar {
    height: 20px;
    background: var(--gray-200);
    border-radius: var(--radius-md);
    overflow: hidden;
    position: relative;
}

.chart-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-md);
    transition: width var(--transition-normal);
}

.chart-fill.commission {
    background: linear-gradient(90deg, var(--success-color), #81c784);
}

.chart-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-800);
    text-align: left;
}

.key-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.metric-card {
    background: var(--bg-secondary);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--neu-shadow-inset);
    text-align: center;
}

.metric-card h4 {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-800);
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.growth-rate {
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.growth-rate.positive {
    color: var(--success-color);
}

.growth-rate.negative {
    color: var(--danger-color);
}

.companies-summary,
.couriers-summary,
.transactions-summary,
.daily-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.summary-card {
    background: var(--bg-secondary);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--neu-shadow-outset);
    text-align: center;
}

.summary-card h4 {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.summary-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.courier-info,
.company-info {
    display: flex;
    flex-direction: column;
}

.courier-info strong,
.company-info strong {
    font-weight: 600;
    color: var(--gray-800);
}

.courier-info small,
.company-info small {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.highlight {
    color: var(--primary-color) !important;
    font-weight: 600;
}

.percentage {
    font-weight: 600;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
}

.percentage.good {
    background: var(--success-light);
    color: var(--success-dark);
}

.percentage.average {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.percentage.low,
.percentage.high {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.transaction-type {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.transaction-type.commission {
    background: var(--success-light);
    color: var(--success-dark);
}

.transaction-type.bonus {
    background: var(--info-light);
    color: var(--info-dark);
}

.transaction-type.deduction {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.amount.positive {
    color: var(--success-color);
}

.amount.negative {
    color: var(--danger-color);
}

/* ===== Reports Styles ===== */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.report-card {
    background: var(--bg-secondary);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow-outset);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--neu-shadow-hover);
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
}

.report-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-2);
}

.report-info p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.report-display {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow-outset);
    margin-top: var(--spacing-8);
}

.report-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.report-header h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-2);
}

.report-meta {
    display: flex;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.report-content {
    padding: var(--spacing-6);
}

.report-section {
    margin-bottom: var(--spacing-8);
}

.report-section:last-child {
    margin-bottom: 0;
}

.report-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--primary-color);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.summary-item {
    background: var(--gray-50);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    text-align: center;
}

.summary-label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.summary-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.return-type-tag {
    display: inline-block;
    background: var(--gray-200);
    color: var(--gray-700);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    margin: 0 var(--spacing-1) var(--spacing-1) 0;
}

.status-badge {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.delivered {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-badge.pending {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-badge.returned {
    background: var(--danger-light);
    color: var(--danger-dark);
}

/* ===== Archive Styles ===== */
.archive-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.monthly-chart .chart-item {
    margin-bottom: var(--spacing-2);
}

.region-item,
.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--neu-shadow-outset);
    margin-bottom: var(--spacing-3);
}

.region-info,
.user-info {
    flex: 1;
}

.region-info strong,
.user-info strong {
    display: block;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.region-info small,
.user-info small {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.region-actions,
.user-actions {
    display: flex;
    gap: var(--spacing-2);
}

.user-status {
    margin: 0 var(--spacing-3);
}

/* ===== Settings Styles ===== */
.settings-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-6);
    margin-top: var(--spacing-6);
}

.settings-sidebar {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow-outset);
    padding: var(--spacing-4);
    height: fit-content;
}

.settings-nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.settings-nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    background: transparent;
    border-radius: var(--radius-lg);
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: right;
    width: 100%;
}

.settings-nav-item:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.settings-nav-item.active {
    background: var(--primary-color);
    color: var(--white);
    box-shadow: var(--neu-shadow-inset);
}

.settings-nav-item i {
    width: 16px;
    text-align: center;
}

.settings-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow-outset);
    padding: var(--spacing-6);
}

.settings-section {
    max-width: 600px;
}

.settings-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--primary-color);
}

.section-actions {
    margin-bottom: var(--spacing-6);
}

.warning-box {
    background: var(--warning-light);
    border: 1px solid var(--warning-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.warning-box i {
    color: var(--warning-color);
    font-size: var(--font-size-lg);
}

.warning-box p {
    margin: 0;
    color: var(--warning-dark);
    font-weight: 500;
}

.backup-actions,
.system-actions {
    margin-top: var(--spacing-6);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

.backup-actions h4,
.system-actions h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
}

.action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-3);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.system-info {
    max-height: 500px;
    overflow-y: auto;
}

.info-section {
    margin-bottom: var(--spacing-6);
}

.info-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--gray-200);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: var(--gray-700);
}

.info-value {
    color: var(--gray-800);
    font-weight: 400;
    max-width: 60%;
    text-align: left;
    word-break: break-word;
}

.info-value.error {
    color: var(--danger-color);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .settings-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .settings-sidebar {
        order: 2;
    }

    .settings-nav {
        flex-direction: row;
        overflow-x: auto;
        gap: var(--spacing-1);
    }

    .settings-nav-item {
        white-space: nowrap;
        min-width: fit-content;
    }

    .financial-stats,
    .charts-section {
        grid-template-columns: 1fr;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .chart-item {
        grid-template-columns: 80px 1fr 60px;
    }

    .action-buttons-grid {
        grid-template-columns: 1fr;
    }
}
