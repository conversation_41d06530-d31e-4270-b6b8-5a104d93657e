/**
 * صفحة المحاسبة
 * Accounting Page
 */

class AccountingPage {
    constructor() {
        this.container = document.getElementById('accounting-page');
        this.currentView = 'overview';
        this.dateFrom = null;
        this.dateTo = null;
        this.selectedCourier = null;
        this.selectedCompany = null;
        this.financialData = {};
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadFinancialData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة المحاسبة:', error);
            toastManager.show('حدث خطأ في تحميل صفحة المحاسبة', 'error');
        }
    }

    /**
     * تحميل البيانات المالية
     * Load financial data
     */
    async loadFinancialData() {
        try {
            // تحديد الفترة الافتراضية (الشهر الحالي)
            const now = new Date();
            this.dateFrom = new Date(now.getFullYear(), now.getMonth(), 1);
            this.dateTo = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            // تحميل البيانات المالية
            this.financialData = {
                totalProfits: await accountingManager.calculateTotalProfits(this.dateFrom, this.dateTo),
                couriersProfits: await accountingManager.generateCouriersProfitReport(this.dateFrom, this.dateTo),
                companiesProfits: await accountingManager.generateCompaniesProfitReport(this.dateFrom, this.dateTo),
                dailyStatement: await accountingManager.generateDailyStatement(new Date()),
                transactions: await accountingManager.getTransactions({
                    dateFrom: this.dateFrom,
                    dateTo: this.dateTo
                })
            };
        } catch (error) {
            console.error('خطأ في تحميل البيانات المالية:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>المحاسبة</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="accountingPage.showAddTransactionModal()">
                        <i class="fas fa-plus"></i>
                        معاملة جديدة
                    </button>
                    <button class="action-btn secondary" onclick="accountingPage.showDateRangeModal()">
                        <i class="fas fa-calendar"></i>
                        تغيير الفترة
                    </button>
                    <button class="action-btn info" onclick="accountingPage.exportFinancialData()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="tabs-container">
                <div class="tabs-nav">
                    <button class="tab-btn ${this.currentView === 'overview' ? 'active' : ''}"
                            onclick="accountingPage.switchView('overview')">
                        <i class="fas fa-chart-pie"></i>
                        نظرة عامة
                    </button>
                    <button class="tab-btn ${this.currentView === 'couriers' ? 'active' : ''}"
                            onclick="accountingPage.switchView('couriers')">
                        <i class="fas fa-users"></i>
                        عمولات المندوبين
                    </button>
                    <button class="tab-btn ${this.currentView === 'companies' ? 'active' : ''}"
                            onclick="accountingPage.switchView('companies')">
                        <i class="fas fa-building"></i>
                        أرباح الشركات
                    </button>
                    <button class="tab-btn ${this.currentView === 'transactions' ? 'active' : ''}"
                            onclick="accountingPage.switchView('transactions')">
                        <i class="fas fa-exchange-alt"></i>
                        المعاملات
                    </button>
                    <button class="tab-btn ${this.currentView === 'daily' ? 'active' : ''}"
                            onclick="accountingPage.switchView('daily')">
                        <i class="fas fa-calendar-day"></i>
                        الكشف اليومي
                    </button>
                </div>

                <div class="tab-content" id="accounting-tab-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    }

    /**
     * رسم المحتوى الحالي
     * Render current view
     */
    renderCurrentView() {
        switch (this.currentView) {
            case 'overview':
                return this.renderOverview();
            case 'couriers':
                return this.renderCouriersView();
            case 'companies':
                return this.renderCompaniesView();
            case 'transactions':
                return this.renderTransactionsView();
            case 'daily':
                return this.renderDailyView();
            default:
                return this.renderOverview();
        }
    }

    /**
     * رسم النظرة العامة
     * Render overview
     */
    renderOverview() {
        const data = this.financialData.totalProfits;
        const period = `${utils.formatDate(this.dateFrom, 'DD/MM/YYYY')} - ${utils.formatDate(this.dateTo, 'DD/MM/YYYY')}`;

        return `
            <div class="overview-section">
                <div class="period-info">
                    <h3>الملخص المالي للفترة: ${period}</h3>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="financial-stats">
                    <div class="stat-card revenue">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${utils.formatCurrency(data.totalRevenue)}</h3>
                            <p>إجمالي الإيرادات</p>
                            <small>${data.totalOrders} طلب</small>
                        </div>
                    </div>

                    <div class="stat-card expenses">
                        <div class="stat-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${utils.formatCurrency(data.totalCommissions)}</h3>
                            <p>إجمالي العمولات</p>
                            <small>${data.profitMargin.toFixed(1)}% هامش ربح</small>
                        </div>
                    </div>

                    <div class="stat-card profit">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${utils.formatCurrency(data.totalProfit)}</h3>
                            <p>صافي الربح</p>
                            <small>${(data.totalRevenue > 0 ? (data.totalProfit / data.totalRevenue * 100) : 0).toFixed(1)}% من الإيرادات</small>
                        </div>
                    </div>

                    <div class="stat-card average">
                        <div class="stat-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${utils.formatCurrency(data.totalOrders > 0 ? data.totalRevenue / data.totalOrders : 0)}</h3>
                            <p>متوسط قيمة الطلب</p>
                            <small>لكل طلب مسلم</small>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="charts-section">
                    <div class="chart-container">
                        <h4>توزيع الإيرادات حسب الشركات</h4>
                        <div class="companies-revenue-chart" id="companies-revenue-chart">
                            ${this.renderCompaniesRevenueChart()}
                        </div>
                    </div>

                    <div class="chart-container">
                        <h4>توزيع العمولات حسب المندوبين</h4>
                        <div class="couriers-commission-chart" id="couriers-commission-chart">
                            ${this.renderCouriersCommissionChart()}
                        </div>
                    </div>
                </div>

                <!-- أهم الإحصائيات -->
                <div class="key-metrics">
                    <div class="metric-card">
                        <h4>أفضل شركة</h4>
                        <div class="metric-value">
                            ${this.getTopCompany()}
                        </div>
                    </div>

                    <div class="metric-card">
                        <h4>أفضل مندوب</h4>
                        <div class="metric-value">
                            ${this.getTopCourier()}
                        </div>
                    </div>

                    <div class="metric-card">
                        <h4>معدل النمو</h4>
                        <div class="metric-value">
                            <span class="growth-rate positive">+12.5%</span>
                            <small>مقارنة بالشهر السابق</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * رسم مخطط إيرادات الشركات
     * Render companies revenue chart
     */
    renderCompaniesRevenueChart() {
        const companies = this.financialData.companiesProfits.companies.slice(0, 5);

        if (companies.length === 0) {
            return '<p class="no-data">لا توجد بيانات</p>';
        }

        let html = '<div class="simple-chart">';
        const maxRevenue = Math.max(...companies.map(c => c.totalRevenue));

        companies.forEach(company => {
            const percentage = maxRevenue > 0 ? (company.totalRevenue / maxRevenue * 100) : 0;
            html += `
                <div class="chart-item">
                    <div class="chart-label">${company.company.name}</div>
                    <div class="chart-bar">
                        <div class="chart-fill" style="width: ${percentage}%"></div>
                    </div>
                    <div class="chart-value">${utils.formatCurrency(company.totalRevenue)}</div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * رسم مخطط عمولات المندوبين
     * Render couriers commission chart
     */
    renderCouriersCommissionChart() {
        const couriers = this.financialData.couriersProfits.couriers.slice(0, 5);

        if (couriers.length === 0) {
            return '<p class="no-data">لا توجد بيانات</p>';
        }

        let html = '<div class="simple-chart">';
        const maxCommission = Math.max(...couriers.map(c => c.totalCommission));

        couriers.forEach(courier => {
            const percentage = maxCommission > 0 ? (courier.totalCommission / maxCommission * 100) : 0;
            html += `
                <div class="chart-item">
                    <div class="chart-label">${courier.courier.name}</div>
                    <div class="chart-bar">
                        <div class="chart-fill commission" style="width: ${percentage}%"></div>
                    </div>
                    <div class="chart-value">${utils.formatCurrency(courier.totalCommission)}</div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * الحصول على أفضل شركة
     * Get top company
     */
    getTopCompany() {
        const companies = this.financialData.companiesProfits.companies;
        if (companies.length === 0) return 'لا توجد بيانات';

        const topCompany = companies[0];
        return `
            <div class="top-item">
                <strong>${topCompany.company.name}</strong>
                <span>${utils.formatCurrency(topCompany.totalRevenue)}</span>
            </div>
        `;
    }

    /**
     * الحصول على أفضل مندوب
     * Get top courier
     */
    getTopCourier() {
        const couriers = this.financialData.couriersProfits.couriers;
        if (couriers.length === 0) return 'لا توجد بيانات';

        const topCourier = couriers[0];
        return `
            <div class="top-item">
                <strong>${topCourier.courier.name}</strong>
                <span>${utils.formatCurrency(topCourier.totalCommission)}</span>
            </div>
        `;
    }

    /**
     * رسم عرض المندوبين
     * Render couriers view
     */
    renderCouriersView() {
        const couriers = this.financialData.couriersProfits.couriers;

        return `
            <div class="couriers-accounting">
                <div class="section-header">
                    <h3>عمولات المندوبين</h3>
                    <div class="section-actions">
                        <button class="action-btn secondary" onclick="accountingPage.showCourierDetailsModal()">
                            <i class="fas fa-user"></i>
                            تفاصيل مندوب
                        </button>
                    </div>
                </div>

                <div class="couriers-summary">
                    <div class="summary-card">
                        <h4>إجمالي العمولات</h4>
                        <span class="summary-value">${utils.formatCurrency(this.financialData.couriersProfits.totalCommissions)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>عدد المندوبين النشطين</h4>
                        <span class="summary-value">${couriers.length}</span>
                    </div>
                    <div class="summary-card">
                        <h4>متوسط العمولة</h4>
                        <span class="summary-value">${utils.formatCurrency(couriers.length > 0 ? this.financialData.couriersProfits.totalCommissions / couriers.length : 0)}</span>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي المبلغ</th>
                                <th>معدل العمولة</th>
                                <th>إجمالي العمولة</th>
                                <th>متوسط العمولة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${couriers.map(courier => `
                                <tr>
                                    <td>
                                        <div class="courier-info">
                                            <strong>${courier.courier.name}</strong>
                                            <small>${courier.courier.region}</small>
                                        </div>
                                    </td>
                                    <td>${courier.totalOrders}</td>
                                    <td>${utils.formatCurrency(courier.totalAmount)}</td>
                                    <td>${courier.commissionRate}%</td>
                                    <td class="highlight">${utils.formatCurrency(courier.totalCommission)}</td>
                                    <td>${utils.formatCurrency(courier.averageCommission)}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-sm btn-primary" onclick="accountingPage.viewCourierDetails(${courier.courier.id})" title="التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-sm btn-success" onclick="accountingPage.payCourierCommission(${courier.courier.id})" title="دفع العمولة">
                                                <i class="fas fa-money-bill"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // لا توجد أحداث إضافية حالياً
    }

    /**
     * تبديل العرض
     * Switch view
     */
    async switchView(view) {
        this.currentView = view;

        // تحديث التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[onclick="accountingPage.switchView('${view}')"]`).classList.add('active');

        // تحديث المحتوى
        document.getElementById('accounting-tab-content').innerHTML = this.renderCurrentView();
    }

    /**
     * عرض نافذة إضافة معاملة
     * Show add transaction modal
     */
    showAddTransactionModal() {
        toastManager.show('سيتم إضافة المعاملات المالية قريباً', 'info');
    }

    /**
     * عرض نافذة تغيير الفترة
     * Show date range modal
     */
    showDateRangeModal() {
        const modalContent = `
            <form id="date-range-form">
                <div class="form-group">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="dateFrom" class="form-input"
                           value="${utils.formatDate(this.dateFrom, 'YYYY-MM-DD')}" required>
                </div>

                <div class="form-group">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="dateTo" class="form-input"
                           value="${utils.formatDate(this.dateTo, 'YYYY-MM-DD')}" required>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="accountingPage.updateDateRange()">تحديث</button>
        `;

        modalManager.show('تغيير الفترة الزمنية', modalContent, { footer });
    }

    /**
     * تحديث الفترة الزمنية
     * Update date range
     */
    async updateDateRange() {
        try {
            const form = document.getElementById('date-range-form');
            const formData = new FormData(form);

            this.dateFrom = new Date(formData.get('dateFrom'));
            this.dateTo = new Date(formData.get('dateTo'));

            modalManager.hide();
            toastManager.show('جاري تحديث البيانات...', 'info');

            await this.loadFinancialData();
            this.render();

            toastManager.show('تم تحديث البيانات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تحديث الفترة:', error);
            toastManager.show('حدث خطأ في تحديث البيانات', 'error');
        }
    }

    /**
     * تصدير البيانات المالية
     * Export financial data
     */
    exportFinancialData() {
        toastManager.show('سيتم إضافة تصدير البيانات المالية قريباً', 'info');
    }

    /**
     * عرض تفاصيل المندوب
     * View courier details
     */
    viewCourierDetails(courierId) {
        toastManager.show('سيتم إضافة تفاصيل المندوب قريباً', 'info');
    }

    /**
     * دفع عمولة المندوب
     * Pay courier commission
     */
    payCourierCommission(courierId) {
        toastManager.show('سيتم إضافة دفع العمولات قريباً', 'info');
    }

    /**
     * رسم عرض الشركات
     * Render companies view
     */
    renderCompaniesView() {
        const companies = this.financialData.companiesProfits.companies;

        return `
            <div class="companies-accounting">
                <div class="section-header">
                    <h3>أرباح الشركات</h3>
                </div>

                <div class="companies-summary">
                    <div class="summary-card">
                        <h4>إجمالي الإيرادات</h4>
                        <span class="summary-value">${utils.formatCurrency(this.financialData.companiesProfits.totalRevenue)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الأرباح</h4>
                        <span class="summary-value">${utils.formatCurrency(this.financialData.companiesProfits.totalProfit)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>عدد الشركات النشطة</h4>
                        <span class="summary-value">${companies.length}</span>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الشركة</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي الإيرادات</th>
                                <th>إجمالي الأرباح</th>
                                <th>هامش الربح</th>
                                <th>متوسط قيمة الطلب</th>
                                <th>معدل الرواجع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${companies.map(company => `
                                <tr>
                                    <td>
                                        <div class="company-info">
                                            <strong>${company.company.name}</strong>
                                            <small>${company.company.phone || ''}</small>
                                        </div>
                                    </td>
                                    <td>${company.totalOrders}</td>
                                    <td>${utils.formatCurrency(company.totalRevenue)}</td>
                                    <td class="highlight">${utils.formatCurrency(company.totalProfit)}</td>
                                    <td>
                                        <span class="percentage ${company.profitMargin > 20 ? 'good' : company.profitMargin > 10 ? 'average' : 'low'}">
                                            ${company.profitMargin.toFixed(1)}%
                                        </span>
                                    </td>
                                    <td>${utils.formatCurrency(company.averageOrderValue)}</td>
                                    <td>
                                        <span class="percentage ${company.returnRate < 5 ? 'good' : company.returnRate < 10 ? 'average' : 'high'}">
                                            ${company.returnRate.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * رسم عرض المعاملات
     * Render transactions view
     */
    renderTransactionsView() {
        const transactions = this.financialData.transactions;

        return `
            <div class="transactions-accounting">
                <div class="section-header">
                    <h3>المعاملات المالية</h3>
                    <div class="section-actions">
                        <button class="action-btn primary" onclick="accountingPage.showAddTransactionModal()">
                            <i class="fas fa-plus"></i>
                            معاملة جديدة
                        </button>
                    </div>
                </div>

                <div class="transactions-summary">
                    <div class="summary-card">
                        <h4>عدد المعاملات</h4>
                        <span class="summary-value">${transactions.length}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي المبالغ</h4>
                        <span class="summary-value">${utils.formatCurrency(transactions.reduce((sum, t) => sum + (t.amount || 0), 0))}</span>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                                <th>المندوب/الشركة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${transactions.length === 0 ?
                                '<tr><td colspan="6" class="no-data">لا توجد معاملات</td></tr>' :
                                transactions.map(transaction => `
                                    <tr>
                                        <td>${utils.formatDate(transaction.date, 'DD/MM/YYYY')}</td>
                                        <td>
                                            <span class="transaction-type ${transaction.type}">
                                                ${this.getTransactionTypeText(transaction.type)}
                                            </span>
                                        </td>
                                        <td class="amount ${transaction.amount > 0 ? 'positive' : 'negative'}">
                                            ${utils.formatCurrency(Math.abs(transaction.amount || 0))}
                                        </td>
                                        <td>${transaction.description || '-'}</td>
                                        <td>${this.getTransactionTarget(transaction)}</td>
                                        <td>
                                            <button class="btn-sm btn-danger" onclick="accountingPage.deleteTransaction(${transaction.id})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * رسم الكشف اليومي
     * Render daily view
     */
    renderDailyView() {
        const daily = this.financialData.dailyStatement;

        return `
            <div class="daily-accounting">
                <div class="section-header">
                    <h3>الكشف اليومي - ${utils.formatDate(daily.date, 'DD/MM/YYYY')}</h3>
                    <div class="section-actions">
                        <button class="action-btn secondary" onclick="accountingPage.changeDailyDate()">
                            <i class="fas fa-calendar"></i>
                            تغيير التاريخ
                        </button>
                    </div>
                </div>

                <div class="daily-summary">
                    <div class="summary-card">
                        <h4>إجمالي الطلبات</h4>
                        <span class="summary-value">${daily.summary.totalOrders}</span>
                    </div>
                    <div class="summary-card">
                        <h4>طلبات مسلمة</h4>
                        <span class="summary-value">${daily.summary.deliveredOrders}</span>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الإيرادات</h4>
                        <span class="summary-value">${utils.formatCurrency(daily.summary.totalRevenue)}</span>
                    </div>
                    <div class="summary-card">
                        <h4>صافي الربح</h4>
                        <span class="summary-value">${utils.formatCurrency(daily.summary.totalProfit)}</span>
                    </div>
                </div>

                <div class="daily-couriers">
                    <h4>أداء المندوبين اليوم</h4>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المندوب</th>
                                    <th>عدد الطلبات</th>
                                    <th>إجمالي الإيرادات</th>
                                    <th>العمولة المستحقة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${daily.courierStats.length === 0 ?
                                    '<tr><td colspan="4" class="no-data">لا توجد طلبات اليوم</td></tr>' :
                                    daily.courierStats.map(stat => `
                                        <tr>
                                            <td>${stat.courier ? stat.courier.name : 'غير محدد'}</td>
                                            <td>${stat.orders}</td>
                                            <td>${utils.formatCurrency(stat.revenue)}</td>
                                            <td class="highlight">${utils.formatCurrency(stat.commission)}</td>
                                        </tr>
                                    `).join('')
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * الحصول على نص نوع المعاملة
     * Get transaction type text
     */
    getTransactionTypeText(type) {
        const types = {
            'commission': 'عمولة مندوب',
            'delivery_fee': 'رسوم توصيل',
            'return_fee': 'رسوم راجع',
            'bonus': 'مكافأة',
            'deduction': 'خصم',
            'adjustment': 'تسوية'
        };
        return types[type] || type;
    }

    /**
     * الحصول على هدف المعاملة
     * Get transaction target
     */
    getTransactionTarget(transaction) {
        if (transaction.courierId) {
            return `مندوب: ${transaction.courierName || 'غير محدد'}`;
        } else if (transaction.companyId) {
            return `شركة: ${transaction.companyName || 'غير محدد'}`;
        }
        return 'عام';
    }

    /**
     * حذف معاملة
     * Delete transaction
     */
    async deleteTransaction(transactionId) {
        modalManager.confirm(
            'تأكيد الحذف',
            'هل أنت متأكد من حذف هذه المعاملة؟',
            async () => {
                try {
                    await db.delete('accounting', transactionId);
                    toastManager.show('تم حذف المعاملة بنجاح', 'success');
                    await this.refresh();
                } catch (error) {
                    console.error('خطأ في حذف المعاملة:', error);
                    toastManager.show('حدث خطأ في حذف المعاملة', 'error');
                }
            }
        );
    }

    /**
     * تغيير تاريخ الكشف اليومي
     * Change daily date
     */
    changeDailyDate() {
        toastManager.show('سيتم إضافة تغيير التاريخ قريباً', 'info');
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadFinancialData();
        this.render();
    }
}

const accountingPage = new AccountingPage();
window.AccountingPage = AccountingPage;
window.accountingPage = accountingPage;
