/**
 * صفحة المحاسبة
 * Accounting Page
 */

class AccountingPage {
    constructor() {
        this.container = document.getElementById('accounting-page');
    }

    async init() {
        this.render();
    }

    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>المحاسبة</h2>
            </div>
            <div class="coming-soon">
                <i class="fas fa-calculator fa-3x"></i>
                <h3>قريباً</h3>
                <p>سيتم إضافة صفحة المحاسبة قريباً</p>
            </div>
        `;
    }

    async refresh() {
        this.render();
    }
}

const accountingPage = new AccountingPage();
window.AccountingPage = AccountingPage;
window.accountingPage = accountingPage;
