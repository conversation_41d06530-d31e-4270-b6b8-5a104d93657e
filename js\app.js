/**
 * التطبيق الرئيسي
 * Main Application
 */

class DeliveryApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentUser = null;
        this.isInitialized = false;
        this.pages = {};
    }

    /**
     * تهيئة التطبيق
     * Initialize application
     */
    async init() {
        try {
            // إظهار شاشة التحميل
            this.showLoadingScreen();

            // تهيئة قاعدة البيانات
            await db.init();

            // تهيئة واجهة المستخدم
            this.initUI();

            // تحميل البيانات الأولية
            await this.loadInitialData();

            // إخفاء شاشة التحميل وإظهار التطبيق
            this.hideLoadingScreen();

            this.isInitialized = true;
            console.log('تم تهيئة التطبيق بنجاح');

        } catch (error) {
            console.error('خطأ في تهيئة التطبيق:', error);
            toastManager.show('حدث خطأ في تحميل التطبيق', 'error');
        }
    }

    /**
     * إظهار شاشة التحميل
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    /**
     * إخفاء شاشة التحميل
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const appContainer = document.getElementById('app');
        
        if (loadingScreen && appContainer) {
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                appContainer.style.display = 'flex';
            }, 1000);
        }
    }

    /**
     * تهيئة واجهة المستخدم
     * Initialize UI
     */
    initUI() {
        // تهيئة الشريط الجانبي
        this.initSidebar();

        // تهيئة الهيدر
        this.initHeader();

        // تهيئة التنقل بين الصفحات
        this.initNavigation();

        // تهيئة الأحداث العامة
        this.initGlobalEvents();
    }

    /**
     * تهيئة الشريط الجانبي
     * Initialize sidebar
     */
    initSidebar() {
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const sidebar = document.getElementById('sidebar');

        // تبديل الشريط الجانبي على الديسكتوب
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }

        // تبديل الشريط الجانبي على الموبايل
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }

        // إغلاق الشريط الجانبي عند النقر خارجه على الموبايل
        document.addEventListener('click', (e) => {
            if (utils && utils.isMobile() &&
                !sidebar.contains(e.target) &&
                mobileMenuToggle && !mobileMenuToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
    }

    /**
     * تهيئة الهيدر
     * Initialize header
     */
    initHeader() {
        const userBtn = document.getElementById('user-btn');
        const userDropdown = document.getElementById('user-dropdown');

        if (userBtn && userDropdown) {
            userBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', () => {
                userDropdown.classList.remove('show');
            });
        }

        // تحديث الإحصائيات في الهيدر
        this.updateHeaderStats();
    }

    /**
     * تهيئة التنقل بين الصفحات
     * Initialize navigation
     */
    initNavigation() {
        const menuItems = document.querySelectorAll('.menu-item');
        
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.getAttribute('data-page');
                if (page) {
                    this.navigateToPage(page);
                }
            });
        });

        // التعامل مع الروابط المباشرة
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash.substring(1);
            if (hash) {
                this.navigateToPage(hash);
            }
        });

        // تحميل الصفحة الأولية
        const initialPage = window.location.hash.substring(1) || 'dashboard';
        this.navigateToPage(initialPage);
    }

    /**
     * تهيئة الأحداث العامة
     * Initialize global events
     */
    initGlobalEvents() {
        // تحديث الإحصائيات كل دقيقة
        setInterval(() => {
            this.updateHeaderStats();
        }, 60000);

        // حفظ حالة التطبيق عند الإغلاق
        window.addEventListener('beforeunload', () => {
            this.saveAppState();
        });

        // التعامل مع تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    /**
     * التنقل إلى صفحة
     * Navigate to page
     */
    navigateToPage(pageName) {
        if (!this.isInitialized) return;

        // إخفاء جميع الصفحات
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.style.display = 'none';
            page.classList.remove('active');
        });

        // إظهار الصفحة المطلوبة
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.style.display = 'block';
            targetPage.classList.add('active');
        }

        // تحديث القائمة النشطة
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-page') === pageName) {
                item.classList.add('active');
            }
        });

        // تحديث عنوان الصفحة
        this.updatePageTitle(pageName);

        // تحديث الرابط
        window.location.hash = pageName;

        // تحميل محتوى الصفحة إذا لم يتم تحميله من قبل
        this.loadPageContent(pageName);

        this.currentPage = pageName;
    }

    /**
     * تحديث عنوان الصفحة
     * Update page title
     */
    updatePageTitle(pageName) {
        const titles = {
            dashboard: 'لوحة التحكم',
            orders: 'إدارة الطلبات',
            couriers: 'إدارة المندوبين',
            companies: 'إدارة الشركات',
            returns: 'إدارة الرواجع',
            accounting: 'المحاسبة',
            reports: 'التقارير',
            archive: 'الأرشيف',
            settings: 'الإعدادات'
        };

        const pageTitle = document.getElementById('page-title');
        if (pageTitle) {
            pageTitle.textContent = titles[pageName] || 'نظام التوصيل';
        }

        // تحديث عنوان المتصفح
        document.title = `${titles[pageName] || 'نظام التوصيل'} - نظام إدارة شركات التوصيل`;
    }

    /**
     * تحميل محتوى الصفحة
     * Load page content
     */
    async loadPageContent(pageName) {
        if (this.pages[pageName]) {
            // إذا كانت الصفحة محملة، قم بتحديثها
            if (this.pages[pageName].refresh) {
                await this.pages[pageName].refresh();
            }
            return;
        }

        try {
            // تحميل محتوى الصفحة حسب النوع
            switch (pageName) {
                case 'dashboard':
                    if (window.DashboardPage) {
                        this.pages[pageName] = new DashboardPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'orders':
                    if (window.OrdersPage) {
                        this.pages[pageName] = new OrdersPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'couriers':
                    if (window.CouriersPage) {
                        this.pages[pageName] = new CouriersPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'companies':
                    if (window.CompaniesPage) {
                        this.pages[pageName] = new CompaniesPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'returns':
                    if (window.ReturnsPage) {
                        this.pages[pageName] = new ReturnsPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'accounting':
                    if (window.AccountingPage) {
                        this.pages[pageName] = new AccountingPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'reports':
                    if (window.ReportsPage) {
                        this.pages[pageName] = new ReportsPage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'archive':
                    if (window.ArchivePage) {
                        this.pages[pageName] = new ArchivePage();
                        await this.pages[pageName].init();
                    }
                    break;
                case 'settings':
                    if (window.SettingsPage) {
                        this.pages[pageName] = new SettingsPage();
                        await this.pages[pageName].init();
                    }
                    break;
            }
        } catch (error) {
            console.error(`خطأ في تحميل صفحة ${pageName}:`, error);
            toastManager.show(`حدث خطأ في تحميل صفحة ${pageName}`, 'error');
        }
    }

    /**
     * تحميل البيانات الأولية
     * Load initial data
     */
    async loadInitialData() {
        try {
            // إنشاء البيانات التجريبية العراقية
            if (window.dataManager && typeof dataManager.createIraqiSampleData === 'function') {
                await dataManager.createIraqiSampleData();
            }

            // تحميل الإعدادات
            const settings = await db.getAll('settings');
            this.settings = {};
            settings.forEach(setting => {
                this.settings[setting.key] = setting.value;
            });

            // تحميل المستخدم الحالي (مؤقتاً)
            this.currentUser = {
                id: 1,
                username: 'admin',
                name: 'المدير العام',
                role: 'admin'
            };

        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
        }
    }

    /**
     * تحديث إحصائيات الهيدر
     * Update header statistics
     */
    async updateHeaderStats() {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // الحصول على طلبات اليوم
            const todayOrders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                orderDate.setHours(0, 0, 0, 0);
                return orderDate.getTime() === today.getTime();
            });

            // حساب أرباح اليوم
            const todayProfit = todayOrders
                .filter(order => order.status === 'delivered')
                .reduce((total, order) => total + (order.amount || 0), 0);

            // تحديث العناصر
            const todayOrdersElement = document.getElementById('today-orders');
            const todayProfitElement = document.getElementById('today-profit');

            if (todayOrdersElement) {
                todayOrdersElement.textContent = todayOrders.length;
            }

            if (todayProfitElement) {
                todayProfitElement.textContent = utils.formatCurrency(todayProfit);
            }

        } catch (error) {
            console.error('خطأ في تحديث إحصائيات الهيدر:', error);
        }
    }

    /**
     * حفظ حالة التطبيق
     * Save app state
     */
    saveAppState() {
        const state = {
            currentPage: this.currentPage,
            sidebarCollapsed: document.getElementById('sidebar').classList.contains('collapsed'),
            timestamp: new Date()
        };

        localStorage.setItem('deliveryAppState', JSON.stringify(state));
    }

    /**
     * استرجاع حالة التطبيق
     * Restore app state
     */
    restoreAppState() {
        try {
            const savedState = localStorage.getItem('deliveryAppState');
            if (savedState) {
                const state = JSON.parse(savedState);
                
                // استرجاع حالة الشريط الجانبي
                if (state.sidebarCollapsed) {
                    document.getElementById('sidebar').classList.add('collapsed');
                }
            }
        } catch (error) {
            console.error('خطأ في استرجاع حالة التطبيق:', error);
        }
    }

    /**
     * التعامل مع تغيير حجم الشاشة
     * Handle resize
     */
    handleResize() {
        const sidebar = document.getElementById('sidebar');
        
        // إغلاق الشريط الجانبي على الموبايل عند تغيير الاتجاه
        if (utils.isMobile()) {
            sidebar.classList.remove('show');
        }
    }
}

// إنشاء مثيل التطبيق
const app = new DeliveryApp();

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});

// تصدير التطبيق للاستخدام العام
window.app = app;
window.navigateToPage = (page) => app.navigateToPage(page);
