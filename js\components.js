/**
 * مكونات واجهة المستخدم
 * UI Components
 */

/**
 * إدارة التنبيهات (Toast Notifications)
 * Toast Notification Manager
 */
class ToastManager {
    constructor() {
        this.container = document.getElementById('toast-container');
        this.toasts = [];
    }

    /**
     * عرض تنبيه
     * Show toast notification
     */
    show(message, type = 'info', duration = 5000) {
        const toast = this.createToast(message, type);
        this.container.appendChild(toast);
        this.toasts.push(toast);

        // إظهار التنبيه
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // إخفاء التنبيه تلقائياً
        if (duration > 0) {
            setTimeout(() => {
                this.hide(toast);
            }, duration);
        }

        return toast;
    }

    /**
     * إنشاء عنصر التنبيه
     * Create toast element
     */
    createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const icon = this.getIcon(type);
        
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${icon}"></i>
            </div>
            <div class="toast-message">${message}</div>
            <button class="toast-close" onclick="toastManager.hide(this.parentElement)">
                <i class="fas fa-times"></i>
            </button>
        `;

        return toast;
    }

    /**
     * الحصول على أيقونة التنبيه
     * Get toast icon
     */
    getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * إخفاء التنبيه
     * Hide toast
     */
    hide(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.parentElement.removeChild(toast);
            }
            this.toasts = this.toasts.filter(t => t !== toast);
        }, 300);
    }

    /**
     * إخفاء جميع التنبيهات
     * Hide all toasts
     */
    hideAll() {
        this.toasts.forEach(toast => this.hide(toast));
    }
}

/**
 * إدارة النوافذ المنبثقة (Modals)
 * Modal Manager
 */
class ModalManager {
    constructor() {
        this.container = document.getElementById('modal-container');
        this.activeModal = null;
    }

    /**
     * عرض نافذة منبثقة
     * Show modal
     */
    show(title, content, options = {}) {
        const modal = this.createModal(title, content, options);
        this.container.appendChild(modal);
        this.activeModal = modal;

        // إظهار النافذة
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);

        // إضافة مستمع للإغلاق بالضغط على الخلفية
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hide();
            }
        });

        // إضافة مستمع للإغلاق بالضغط على Escape
        document.addEventListener('keydown', this.handleEscapeKey.bind(this));

        return modal;
    }

    /**
     * إنشاء عنصر النافذة المنبثقة
     * Create modal element
     */
    createModal(title, content, options) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal';
        modalContent.style.width = options.width || 'auto';
        modalContent.style.maxWidth = options.maxWidth || '600px';

        modalContent.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                <button class="modal-close" onclick="modalManager.hide()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            ${options.footer ? `<div class="modal-footer">${options.footer}</div>` : ''}
        `;

        modal.appendChild(modalContent);
        return modal;
    }

    /**
     * إخفاء النافذة المنبثقة
     * Hide modal
     */
    hide() {
        if (this.activeModal) {
            this.activeModal.classList.remove('show');
            setTimeout(() => {
                if (this.activeModal && this.activeModal.parentElement) {
                    this.activeModal.parentElement.removeChild(this.activeModal);
                }
                this.activeModal = null;
            }, 300);
        }
        document.removeEventListener('keydown', this.handleEscapeKey.bind(this));
    }

    /**
     * التعامل مع مفتاح Escape
     * Handle escape key
     */
    handleEscapeKey(e) {
        if (e.key === 'Escape') {
            this.hide();
        }
    }

    /**
     * عرض نافذة تأكيد
     * Show confirmation modal
     */
    confirm(title, message, onConfirm, onCancel) {
        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide(); ${onCancel ? onCancel.toString() + '()' : ''}">
                إلغاء
            </button>
            <button class="action-btn danger" onclick="modalManager.hide(); ${onConfirm.toString()}()">
                تأكيد
            </button>
        `;

        return this.show(title, `<p>${message}</p>`, { footer });
    }
}

/**
 * إدارة الجداول
 * Table Manager
 */
class TableManager {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            searchable: true,
            sortable: true,
            paginated: true,
            pageSize: 10,
            ...options
        };
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
    }

    /**
     * تحديد البيانات
     * Set data
     */
    setData(data) {
        this.data = data;
        this.filteredData = [...data];
        this.currentPage = 1;
        this.render();
    }

    /**
     * إضافة صف
     * Add row
     */
    addRow(row) {
        this.data.push(row);
        this.filteredData = [...this.data];
        this.render();
    }

    /**
     * تحديث صف
     * Update row
     */
    updateRow(index, row) {
        this.data[index] = row;
        this.filteredData = [...this.data];
        this.render();
    }

    /**
     * حذف صف
     * Delete row
     */
    deleteRow(index) {
        this.data.splice(index, 1);
        this.filteredData = [...this.data];
        this.render();
    }

    /**
     * البحث في البيانات
     * Search data
     */
    search(query) {
        if (!query) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(row => {
                return Object.values(row).some(value => 
                    value.toString().toLowerCase().includes(query.toLowerCase())
                );
            });
        }
        this.currentPage = 1;
        this.render();
    }

    /**
     * ترتيب البيانات
     * Sort data
     */
    sort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.filteredData.sort((a, b) => {
            let aValue = a[column];
            let bValue = b[column];

            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.render();
    }

    /**
     * الانتقال إلى صفحة
     * Go to page
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }

    /**
     * رسم الجدول
     * Render table
     */
    render() {
        if (!this.container) return;

        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        let html = '';

        // شريط البحث
        if (this.options.searchable) {
            html += `
                <div class="table-controls">
                    <div class="search-box">
                        <input type="text" placeholder="البحث..." onkeyup="this.parentElement.parentElement.tableManager.search(this.value)">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            `;
        }

        // الجدول
        html += '<table class="table">';
        
        // رأس الجدول
        if (this.options.columns) {
            html += '<thead><tr>';
            this.options.columns.forEach(column => {
                const sortIcon = this.sortColumn === column.key ? 
                    (this.sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down') : 'fa-sort';
                
                html += `
                    <th ${this.options.sortable ? `onclick="this.tableManager.sort('${column.key}')"` : ''}>
                        ${column.title}
                        ${this.options.sortable ? `<i class="fas ${sortIcon}"></i>` : ''}
                    </th>
                `;
            });
            html += '</tr></thead>';
        }

        // محتوى الجدول
        html += '<tbody>';
        if (pageData.length === 0) {
            html += `<tr><td colspan="${this.options.columns?.length || 1}" class="no-data">لا توجد بيانات</td></tr>`;
        } else {
            pageData.forEach((row, index) => {
                html += '<tr>';
                if (this.options.columns) {
                    this.options.columns.forEach(column => {
                        let value = row[column.key];
                        if (column.render) {
                            value = column.render(value, row, index);
                        }
                        html += `<td>${value}</td>`;
                    });
                } else {
                    Object.values(row).forEach(value => {
                        html += `<td>${value}</td>`;
                    });
                }
                html += '</tr>';
            });
        }
        html += '</tbody></table>';

        // التنقل بين الصفحات
        if (this.options.paginated && this.filteredData.length > this.options.pageSize) {
            const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
            html += '<div class="pagination">';
            
            // الصفحة السابقة
            html += `<button ${this.currentPage === 1 ? 'disabled' : ''} onclick="this.tableManager.goToPage(${this.currentPage - 1})">السابق</button>`;
            
            // أرقام الصفحات
            for (let i = 1; i <= totalPages; i++) {
                html += `<button class="${i === this.currentPage ? 'active' : ''}" onclick="this.tableManager.goToPage(${i})">${i}</button>`;
            }
            
            // الصفحة التالية
            html += `<button ${this.currentPage === totalPages ? 'disabled' : ''} onclick="this.tableManager.goToPage(${this.currentPage + 1})">التالي</button>`;
            
            html += '</div>';
        }

        this.container.innerHTML = html;
        
        // ربط مرجع الجدول بالعناصر
        this.container.querySelectorAll('input, button, th').forEach(element => {
            element.tableManager = this;
        });
    }
}

/**
 * إدارة النماذج
 * Form Manager
 */
class FormManager {
    constructor(formId) {
        this.form = document.getElementById(formId);
        this.validators = {};
    }

    /**
     * إضافة مُحقق
     * Add validator
     */
    addValidator(fieldName, validator) {
        this.validators[fieldName] = validator;
    }

    /**
     * التحقق من صحة النموذج
     * Validate form
     */
    validate() {
        let isValid = true;
        const errors = {};

        Object.keys(this.validators).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                const validator = this.validators[fieldName];
                const result = validator(field.value);
                
                if (result !== true) {
                    isValid = false;
                    errors[fieldName] = result;
                    this.showFieldError(field, result);
                } else {
                    this.clearFieldError(field);
                }
            }
        });

        return { isValid, errors };
    }

    /**
     * عرض خطأ الحقل
     * Show field error
     */
    showFieldError(field, message) {
        field.classList.add('error');
        
        let errorElement = field.parentElement.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentElement.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    }

    /**
     * إزالة خطأ الحقل
     * Clear field error
     */
    clearFieldError(field) {
        field.classList.remove('error');
        
        const errorElement = field.parentElement.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * الحصول على بيانات النموذج
     * Get form data
     */
    getData() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }

    /**
     * تعيين بيانات النموذج
     * Set form data
     */
    setData(data) {
        Object.keys(data).forEach(key => {
            const field = this.form.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = data[key];
            }
        });
    }

    /**
     * إعادة تعيين النموذج
     * Reset form
     */
    reset() {
        this.form.reset();
        this.form.querySelectorAll('.error').forEach(field => {
            this.clearFieldError(field);
        });
    }
}

// إنشاء مثيلات المديرين
const toastManager = new ToastManager();
const modalManager = new ModalManager();

// تصدير المكونات للاستخدام العام
window.ToastManager = ToastManager;
window.ModalManager = ModalManager;
window.TableManager = TableManager;
window.FormManager = FormManager;
window.toastManager = toastManager;
window.modalManager = modalManager;
