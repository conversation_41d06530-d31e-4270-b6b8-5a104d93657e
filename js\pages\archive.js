/**
 * صفحة الأرشيف
 * Archive Page
 */

class ArchivePage {
    constructor() {
        this.container = document.getElementById('archive-page');
    }

    async init() {
        this.render();
    }

    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>الأرشيف</h2>
            </div>
            <div class="coming-soon">
                <i class="fas fa-archive fa-3x"></i>
                <h3>قريباً</h3>
                <p>سيتم إضافة صفحة الأرشيف قريباً</p>
            </div>
        `;
    }

    async refresh() {
        this.render();
    }
}

const archivePage = new ArchivePage();
window.ArchivePage = ArchivePage;
window.archivePage = archivePage;
