/**
 * صفحة الأرشيف
 * Archive Page
 */

class ArchivePage {
    constructor() {
        this.container = document.getElementById('archive-page');
        this.tableManager = null;
        this.archivedOrders = [];
        this.companies = [];
        this.couriers = [];
        this.currentView = 'orders';
        this.filters = {
            dateFrom: null,
            dateTo: null,
            companyId: null,
            courierId: null,
            status: null
        };
    }

    /**
     * تهيئة الصفحة
     * Initialize page
     */
    async init() {
        try {
            await this.loadData();
            this.render();
            this.bindEvents();
        } catch (error) {
            console.error('خطأ في تهيئة صفحة الأرشيف:', error);
            toastManager.show('حدث خطأ في تحميل صفحة الأرشيف', 'error');
        }
    }

    /**
     * تحميل البيانات
     * Load data
     */
    async loadData() {
        try {
            this.archivedOrders = await db.getAll('orders', order => order.isArchived);
            this.companies = await db.getAll('companies');
            this.couriers = await db.getAll('couriers');
        } catch (error) {
            console.error('خطأ في تحميل بيانات الأرشيف:', error);
        }
    }

    /**
     * رسم الصفحة
     * Render page
     */
    render() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="page-header">
                <h2>الأرشيف</h2>
                <div class="page-actions">
                    <button class="action-btn primary" onclick="archivePage.showArchiveOrdersModal()">
                        <i class="fas fa-archive"></i>
                        أرشفة طلبات
                    </button>
                    <button class="action-btn secondary" onclick="archivePage.showFiltersModal()">
                        <i class="fas fa-filter"></i>
                        فلترة
                    </button>
                    <button class="action-btn info" onclick="archivePage.exportArchive()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button class="action-btn warning" onclick="archivePage.showCleanupModal()">
                        <i class="fas fa-broom"></i>
                        تنظيف الأرشيف
                    </button>
                </div>
            </div>

            <!-- إحصائيات الأرشيف -->
            <div class="archive-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-archive"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.archivedOrders.length}</h3>
                        <p>إجمالي الطلبات المؤرشفة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.archivedOrders.filter(o => o.status === 'delivered').length}</h3>
                        <p>طلبات مسلمة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <i class="fas fa-undo"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${this.archivedOrders.filter(o => o.status === 'returned' || o.status === 'partial_return').length}</h3>
                        <p>طلبات راجعة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <h3>${utils.formatCurrency(this.archivedOrders.reduce((sum, o) => sum + (o.amount || 0), 0))}</h3>
                        <p>إجمالي المبالغ</p>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="tabs-container">
                <div class="tabs-nav">
                    <button class="tab-btn ${this.currentView === 'orders' ? 'active' : ''}"
                            onclick="archivePage.switchView('orders')">
                        <i class="fas fa-list"></i>
                        الطلبات المؤرشفة
                    </button>
                    <button class="tab-btn ${this.currentView === 'statistics' ? 'active' : ''}"
                            onclick="archivePage.switchView('statistics')">
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات الأرشيف
                    </button>
                </div>

                <div class="tab-content" id="archive-tab-content">
                    ${this.renderCurrentView()}
                </div>
            </div>
        `;
    }

    /**
     * رسم المحتوى الحالي
     * Render current view
     */
    renderCurrentView() {
        switch (this.currentView) {
            case 'orders':
                return this.renderOrdersView();
            case 'statistics':
                return this.renderStatisticsView();
            default:
                return this.renderOrdersView();
        }
    }

    /**
     * رسم عرض الطلبات
     * Render orders view
     */
    renderOrdersView() {
        return `
            <div class="archive-orders">
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label>البحث</label>
                            <input type="text" id="search-input" placeholder="رقم الوصل، اسم الزبون، أو الهاتف">
                        </div>
                        <div class="filter-group">
                            <label>الشركة</label>
                            <select id="company-filter">
                                <option value="">جميع الشركات</option>
                                ${this.companies.map(company =>
                                    `<option value="${company.id}">${company.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>المندوب</label>
                            <select id="courier-filter">
                                <option value="">جميع المندوبين</option>
                                ${this.couriers.map(courier =>
                                    `<option value="${courier.id}">${courier.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الحالة</label>
                            <select id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="delivered">مُسلم</option>
                                <option value="returned">راجع</option>
                                <option value="partial_return">راجع جزئي</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button class="action-btn primary" onclick="archivePage.applyFilters()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <button class="action-btn secondary" onclick="archivePage.clearFilters()">
                                <i class="fas fa-times"></i>
                                مسح
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-container" id="archive-table-container">
                    <!-- سيتم إدراج الجدول هنا -->
                </div>
            </div>
        `;
    }

    /**
     * رسم عرض الإحصائيات
     * Render statistics view
     */
    renderStatisticsView() {
        const stats = this.calculateArchiveStatistics();

        return `
            <div class="archive-statistics">
                <div class="stats-section">
                    <h4>إحصائيات عامة</h4>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">إجمالي الطلبات</span>
                            <span class="stat-value">${stats.totalOrders}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">معدل التسليم</span>
                            <span class="stat-value">${stats.deliveryRate.toFixed(1)}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">معدل الرواجع</span>
                            <span class="stat-value">${stats.returnRate.toFixed(1)}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">متوسط قيمة الطلب</span>
                            <span class="stat-value">${utils.formatCurrency(stats.averageOrderValue)}</span>
                        </div>
                    </div>
                </div>

                <div class="stats-section">
                    <h4>الطلبات حسب الشهر</h4>
                    <div class="monthly-chart">
                        ${this.renderMonthlyChart(stats.monthlyData)}
                    </div>
                </div>

                <div class="stats-section">
                    <h4>أفضل الشركات</h4>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>الشركة</th>
                                    <th>عدد الطلبات</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>معدل التسليم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${stats.topCompanies.map(company => `
                                    <tr>
                                        <td>${company.name}</td>
                                        <td>${company.orders}</td>
                                        <td>${utils.formatCurrency(company.amount)}</td>
                                        <td>
                                            <span class="percentage ${company.deliveryRate > 90 ? 'good' : company.deliveryRate > 80 ? 'average' : 'low'}">
                                                ${company.deliveryRate.toFixed(1)}%
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="stats-section">
                    <h4>أفضل المندوبين</h4>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المندوب</th>
                                    <th>عدد الطلبات</th>
                                    <th>معدل النجاح</th>
                                    <th>إجمالي العمولة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${stats.topCouriers.map(courier => `
                                    <tr>
                                        <td>${courier.name}</td>
                                        <td>${courier.orders}</td>
                                        <td>
                                            <span class="percentage ${courier.successRate > 90 ? 'good' : courier.successRate > 80 ? 'average' : 'low'}">
                                                ${courier.successRate.toFixed(1)}%
                                            </span>
                                        </td>
                                        <td>${utils.formatCurrency(courier.commission)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * رسم الجدول
     * Render table
     */
    renderTable() {
        const tableContainer = document.getElementById('archive-table-container');
        if (!tableContainer) return;

        const columns = [
            { key: 'receiptNumber', title: 'رقم الوصل' },
            { key: 'companyName', title: 'الشركة' },
            { key: 'customerName', title: 'اسم الزبون' },
            { key: 'amount', title: 'المبلغ', render: (value) => utils.formatCurrency(value) },
            { key: 'courierName', title: 'المندوب' },
            { key: 'status', title: 'الحالة', render: (value) => this.renderStatusBadge(value) },
            { key: 'createdDate', title: 'تاريخ الإنشاء', render: (value) => utils.formatDate(value, 'DD/MM/YYYY') },
            { key: 'archivedDate', title: 'تاريخ الأرشفة', render: (value) => value ? utils.formatDate(value, 'DD/MM/YYYY') : '-' },
            { key: 'actions', title: 'الإجراءات', render: (value, row) => this.renderActions(row) }
        ];

        this.tableManager = new TableManager('archive-table-container', {
            columns: columns,
            searchable: false,
            sortable: true,
            paginated: true,
            pageSize: 20
        });

        this.prepareTableData();
    }

    /**
     * تحضير بيانات الجدول
     * Prepare table data
     */
    async prepareTableData() {
        const tableData = [];

        for (const order of this.archivedOrders) {
            const company = this.companies.find(c => c.id === order.companyId);
            const courier = this.couriers.find(c => c.id === order.courierId);

            tableData.push({
                ...order,
                companyName: company ? company.name : 'غير محدد',
                courierName: courier ? courier.name : 'غير مُسند'
            });
        }

        this.tableManager.setData(tableData);
    }

    /**
     * رسم شارة الحالة
     * Render status badge
     */
    renderStatusBadge(status) {
        const statusConfig = {
            'delivered': { text: 'مُسلم', class: 'success' },
            'returned': { text: 'راجع', class: 'danger' },
            'partial_return': { text: 'راجع جزئي', class: 'warning' }
        };

        const config = statusConfig[status] || { text: 'غير محدد', class: 'secondary' };
        return `<span class="badge badge-${config.class}">${config.text}</span>`;
    }

    /**
     * رسم أزرار الإجراءات
     * Render action buttons
     */
    renderActions(order) {
        return `
            <div class="action-buttons">
                <button class="btn-sm btn-primary" onclick="archivePage.viewOrder(${order.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-sm btn-success" onclick="archivePage.restoreOrder(${order.id})" title="استعادة">
                    <i class="fas fa-undo"></i>
                </button>
                <button class="btn-sm btn-danger" onclick="archivePage.deleteOrder(${order.id})" title="حذف نهائي">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    /**
     * ربط الأحداث
     * Bind events
     */
    bindEvents() {
        // البحث المباشر
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('keyup', () => {
                this.applyFilters();
            });
        }

        // تطبيق الفلاتر عند تغيير القيم
        ['company-filter', 'courier-filter', 'status-filter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', () => {
                    this.applyFilters();
                });
            }
        });
    }

    /**
     * تبديل العرض
     * Switch view
     */
    async switchView(view) {
        this.currentView = view;

        // تحديث التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[onclick="archivePage.switchView('${view}')"]`).classList.add('active');

        // تحديث المحتوى
        document.getElementById('archive-tab-content').innerHTML = this.renderCurrentView();

        // إعادة رسم الجدول إذا كان في عرض الطلبات
        if (view === 'orders') {
            setTimeout(() => {
                this.renderTable();
                this.bindEvents();
            }, 100);
        }
    }

    /**
     * حساب إحصائيات الأرشيف
     * Calculate archive statistics
     */
    calculateArchiveStatistics() {
        const totalOrders = this.archivedOrders.length;
        const deliveredOrders = this.archivedOrders.filter(o => o.status === 'delivered').length;
        const returnedOrders = this.archivedOrders.filter(o => o.status === 'returned' || o.status === 'partial_return').length;

        const deliveryRate = totalOrders > 0 ? (deliveredOrders / totalOrders * 100) : 0;
        const returnRate = totalOrders > 0 ? (returnedOrders / totalOrders * 100) : 0;

        const totalAmount = this.archivedOrders.reduce((sum, o) => sum + (o.amount || 0), 0);
        const averageOrderValue = totalOrders > 0 ? totalAmount / totalOrders : 0;

        // البيانات الشهرية
        const monthlyData = this.calculateMonthlyData();

        // أفضل الشركات
        const topCompanies = this.calculateTopCompanies();

        // أفضل المندوبين
        const topCouriers = this.calculateTopCouriers();

        return {
            totalOrders,
            deliveredOrders,
            returnedOrders,
            deliveryRate,
            returnRate,
            totalAmount,
            averageOrderValue,
            monthlyData,
            topCompanies,
            topCouriers
        };
    }

    /**
     * حساب البيانات الشهرية
     * Calculate monthly data
     */
    calculateMonthlyData() {
        const monthlyData = {};

        this.archivedOrders.forEach(order => {
            const date = new Date(order.createdDate);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = {
                    month: monthKey,
                    orders: 0,
                    amount: 0,
                    delivered: 0
                };
            }

            monthlyData[monthKey].orders++;
            monthlyData[monthKey].amount += order.amount || 0;
            if (order.status === 'delivered') {
                monthlyData[monthKey].delivered++;
            }
        });

        return Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));
    }

    /**
     * حساب أفضل الشركات
     * Calculate top companies
     */
    calculateTopCompanies() {
        const companiesData = {};

        this.archivedOrders.forEach(order => {
            const company = this.companies.find(c => c.id === order.companyId);
            const companyName = company ? company.name : 'غير محدد';

            if (!companiesData[companyName]) {
                companiesData[companyName] = {
                    name: companyName,
                    orders: 0,
                    amount: 0,
                    delivered: 0
                };
            }

            companiesData[companyName].orders++;
            companiesData[companyName].amount += order.amount || 0;
            if (order.status === 'delivered') {
                companiesData[companyName].delivered++;
            }
        });

        return Object.values(companiesData)
            .map(company => ({
                ...company,
                deliveryRate: company.orders > 0 ? (company.delivered / company.orders * 100) : 0
            }))
            .sort((a, b) => b.orders - a.orders)
            .slice(0, 10);
    }

    /**
     * حساب أفضل المندوبين
     * Calculate top couriers
     */
    calculateTopCouriers() {
        const couriersData = {};

        this.archivedOrders.forEach(order => {
            if (!order.courierId) return;

            const courier = this.couriers.find(c => c.id === order.courierId);
            const courierName = courier ? courier.name : 'غير محدد';

            if (!couriersData[courierName]) {
                couriersData[courierName] = {
                    name: courierName,
                    orders: 0,
                    delivered: 0,
                    commission: 0,
                    commissionRate: courier ? courier.commission : 0
                };
            }

            couriersData[courierName].orders++;
            if (order.status === 'delivered') {
                couriersData[courierName].delivered++;
                couriersData[courierName].commission += (order.amount || 0) * (couriersData[courierName].commissionRate || 0) / 100;
            }
        });

        return Object.values(couriersData)
            .map(courier => ({
                ...courier,
                successRate: courier.orders > 0 ? (courier.delivered / courier.orders * 100) : 0
            }))
            .sort((a, b) => b.orders - a.orders)
            .slice(0, 10);
    }

    /**
     * رسم المخطط الشهري
     * Render monthly chart
     */
    renderMonthlyChart(monthlyData) {
        if (monthlyData.length === 0) {
            return '<p class="no-data">لا توجد بيانات</p>';
        }

        const maxOrders = Math.max(...monthlyData.map(m => m.orders));

        let html = '<div class="simple-chart monthly-chart">';

        monthlyData.forEach(month => {
            const percentage = maxOrders > 0 ? (month.orders / maxOrders * 100) : 0;
            html += `
                <div class="chart-item">
                    <div class="chart-label">${month.month}</div>
                    <div class="chart-bar">
                        <div class="chart-fill" style="width: ${percentage}%"></div>
                    </div>
                    <div class="chart-value">${month.orders}</div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * تطبيق الفلاتر
     * Apply filters
     */
    applyFilters() {
        const searchQuery = document.getElementById('search-input')?.value || '';
        const companyId = document.getElementById('company-filter')?.value || '';
        const courierId = document.getElementById('courier-filter')?.value || '';
        const status = document.getElementById('status-filter')?.value || '';

        let filteredOrders = [...this.archivedOrders];

        if (searchQuery) {
            filteredOrders = filteredOrders.filter(order =>
                order.receiptNumber.includes(searchQuery) ||
                order.customerName.includes(searchQuery) ||
                order.customerPhone.includes(searchQuery)
            );
        }

        if (companyId) {
            filteredOrders = filteredOrders.filter(order =>
                order.companyId === parseInt(companyId)
            );
        }

        if (courierId) {
            filteredOrders = filteredOrders.filter(order =>
                order.courierId === parseInt(courierId)
            );
        }

        if (status) {
            filteredOrders = filteredOrders.filter(order =>
                order.status === status
            );
        }

        // تحديث الجدول
        this.archivedOrders = filteredOrders;
        this.prepareTableData();
    }

    /**
     * مسح الفلاتر
     * Clear filters
     */
    async clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('company-filter').value = '';
        document.getElementById('courier-filter').value = '';
        document.getElementById('status-filter').value = '';

        await this.loadData();
        this.prepareTableData();
    }

    /**
     * عرض نافذة أرشفة الطلبات
     * Show archive orders modal
     */
    showArchiveOrdersModal() {
        const modalContent = `
            <form id="archive-orders-form">
                <div class="form-group">
                    <label class="form-label">معايير الأرشفة</label>
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="archiveDelivered" checked>
                            <span>الطلبات المسلمة</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="archiveReturned" checked>
                            <span>الطلبات الراجعة</span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">أرشفة الطلبات الأقدم من</label>
                    <select name="archivePeriod" class="form-select" required>
                        <option value="30">30 يوم</option>
                        <option value="60">60 يوم</option>
                        <option value="90" selected>90 يوم</option>
                        <option value="180">180 يوم</option>
                        <option value="365">سنة واحدة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" class="form-textarea" rows="3"
                              placeholder="ملاحظات حول عملية الأرشفة"></textarea>
                </div>
            </form>
        `;

        const footer = `
            <button class="action-btn secondary" onclick="modalManager.hide()">إلغاء</button>
            <button class="action-btn primary" onclick="archivePage.processArchiveOrders()">أرشفة الطلبات</button>
        `;

        modalManager.show('أرشفة الطلبات', modalContent, { footer });
    }

    /**
     * معالجة أرشفة الطلبات
     * Process archive orders
     */
    async processArchiveOrders() {
        try {
            const form = document.getElementById('archive-orders-form');
            const formData = new FormData(form);

            const archiveDelivered = formData.get('archiveDelivered') === 'on';
            const archiveReturned = formData.get('archiveReturned') === 'on';
            const archivePeriod = parseInt(formData.get('archivePeriod'));
            const notes = formData.get('notes');

            if (!archiveDelivered && !archiveReturned) {
                toastManager.show('يرجى اختيار نوع واحد على الأقل للأرشفة', 'warning');
                return;
            }

            // حساب التاريخ المحدد
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - archivePeriod);

            // الحصول على الطلبات المؤهلة للأرشفة
            const ordersToArchive = await db.getAll('orders', order => {
                if (order.isArchived) return false;

                const orderDate = new Date(order.createdDate);
                if (orderDate > cutoffDate) return false;

                if (archiveDelivered && order.status === 'delivered') return true;
                if (archiveReturned && (order.status === 'returned' || order.status === 'partial_return')) return true;

                return false;
            });

            if (ordersToArchive.length === 0) {
                toastManager.show('لا توجد طلبات مؤهلة للأرشفة', 'info');
                modalManager.hide();
                return;
            }

            // تأكيد الأرشفة
            modalManager.confirm(
                'تأكيد الأرشفة',
                `سيتم أرشفة ${ordersToArchive.length} طلب. هل تريد المتابعة؟`,
                async () => {
                    try {
                        // أرشفة الطلبات
                        for (const order of ordersToArchive) {
                            await db.update('orders', {
                                ...order,
                                isArchived: true,
                                archivedDate: new Date(),
                                archiveNotes: notes
                            });
                        }

                        modalManager.hide();
                        toastManager.show(`تم أرشفة ${ordersToArchive.length} طلب بنجاح`, 'success');
                        await this.refresh();

                    } catch (error) {
                        console.error('خطأ في أرشفة الطلبات:', error);
                        toastManager.show('حدث خطأ في أرشفة الطلبات', 'error');
                    }
                }
            );

        } catch (error) {
            console.error('خطأ في معالجة أرشفة الطلبات:', error);
            toastManager.show('حدث خطأ في معالجة الأرشفة', 'error');
        }
    }

    /**
     * عرض تفاصيل الطلب
     * View order details
     */
    async viewOrder(orderId) {
        if (window.viewOrderDetails) {
            await window.viewOrderDetails(orderId);
        }
    }

    /**
     * استعادة طلب من الأرشيف
     * Restore order from archive
     */
    async restoreOrder(orderId) {
        modalManager.confirm(
            'تأكيد الاستعادة',
            'هل أنت متأكد من استعادة هذا الطلب من الأرشيف؟',
            async () => {
                try {
                    const order = await db.getById('orders', orderId);
                    if (!order) {
                        toastManager.show('لم يتم العثور على الطلب', 'error');
                        return;
                    }

                    await db.update('orders', {
                        ...order,
                        isArchived: false,
                        archivedDate: null,
                        archiveNotes: null,
                        restoredDate: new Date()
                    });

                    toastManager.show('تم استعادة الطلب بنجاح', 'success');
                    await this.refresh();

                } catch (error) {
                    console.error('خطأ في استعادة الطلب:', error);
                    toastManager.show('حدث خطأ في استعادة الطلب', 'error');
                }
            }
        );
    }

    /**
     * حذف طلب نهائياً
     * Delete order permanently
     */
    deleteOrder(orderId) {
        modalManager.confirm(
            'تأكيد الحذف النهائي',
            'هل أنت متأكد من حذف هذا الطلب نهائياً؟ لا يمكن التراجع عن هذا الإجراء.',
            async () => {
                try {
                    await db.delete('orders', orderId);
                    toastManager.show('تم حذف الطلب نهائياً', 'success');
                    await this.refresh();

                } catch (error) {
                    console.error('خطأ في حذف الطلب:', error);
                    toastManager.show('حدث خطأ في حذف الطلب', 'error');
                }
            }
        );
    }

    /**
     * عرض نافذة الفلاتر
     * Show filters modal
     */
    showFiltersModal() {
        toastManager.show('استخدم الفلاتر في الأعلى', 'info');
    }

    /**
     * تصدير الأرشيف
     * Export archive
     */
    exportArchive() {
        try {
            const exportData = this.archivedOrders.map(order => {
                const company = this.companies.find(c => c.id === order.companyId);
                const courier = this.couriers.find(c => c.id === order.courierId);

                return {
                    'رقم الوصل': order.receiptNumber,
                    'الشركة': company ? company.name : 'غير محدد',
                    'اسم الزبون': order.customerName,
                    'هاتف الزبون': order.customerPhone,
                    'العنوان': order.address,
                    'المبلغ': order.amount,
                    'المندوب': courier ? courier.name : 'غير مُسند',
                    'الحالة': this.getStatusText(order.status),
                    'تاريخ الإنشاء': utils.formatDate(order.createdDate, 'DD/MM/YYYY'),
                    'تاريخ الأرشفة': order.archivedDate ? utils.formatDate(order.archivedDate, 'DD/MM/YYYY') : '',
                    'ملاحظات الأرشفة': order.archiveNotes || ''
                };
            });

            utils.exportToCSV(exportData, `archive_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`);
            toastManager.show('تم تصدير الأرشيف بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير الأرشيف:', error);
            toastManager.show('حدث خطأ في تصدير الأرشيف', 'error');
        }
    }

    /**
     * عرض نافذة تنظيف الأرشيف
     * Show cleanup modal
     */
    showCleanupModal() {
        toastManager.show('سيتم إضافة تنظيف الأرشيف قريباً', 'info');
    }

    /**
     * الحصول على نص الحالة
     * Get status text
     */
    getStatusText(status) {
        const statusTexts = {
            'delivered': 'مُسلم',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * تحديث الصفحة
     * Refresh page
     */
    async refresh() {
        await this.loadData();
        this.render();
        if (this.currentView === 'orders') {
            setTimeout(() => {
                this.renderTable();
                this.bindEvents();
            }, 100);
        }
    }
}

const archivePage = new ArchivePage();
window.ArchivePage = ArchivePage;
window.archivePage = archivePage;
