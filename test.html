<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة شركات التوصيل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2563eb;
            margin-top: 0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2563eb;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام إدارة شركات التوصيل</h1>
        <p>هذه الصفحة لاختبار جميع وظائف النظام والتأكد من عمله بشكل صحيح.</p>

        <div class="test-section">
            <h3>📊 إحصائيات النظام</h3>
            <div class="stats" id="system-stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-orders">-</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-couriers">-</div>
                    <div class="stat-label">عدد المندوبين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-companies">-</div>
                    <div class="stat-label">عدد الشركات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-returns">-</div>
                    <div class="stat-label">عدد الرواجع</div>
                </div>
            </div>
            <button onclick="loadSystemStats()">تحديث الإحصائيات</button>
        </div>

        <div class="test-section">
            <h3>🗄️ اختبار قاعدة البيانات</h3>
            <div id="db-test-results"></div>
            <button onclick="testDatabase()">اختبار قاعدة البيانات</button>
            <button onclick="clearDatabase()">مسح قاعدة البيانات</button>
        </div>

        <div class="test-section">
            <h3>👥 اختبار إدارة المندوبين</h3>
            <div id="courier-test-results"></div>
            <button onclick="testCourierManagement()">اختبار المندوبين</button>
        </div>

        <div class="test-section">
            <h3>🏢 اختبار إدارة الشركات</h3>
            <div id="company-test-results"></div>
            <button onclick="testCompanyManagement()">اختبار الشركات</button>
        </div>

        <div class="test-section">
            <h3>📦 اختبار إدارة الطلبات</h3>
            <div id="order-test-results"></div>
            <button onclick="testOrderManagement()">اختبار الطلبات</button>
        </div>

        <div class="test-section">
            <h3>🔄 اختبار إدارة الرواجع</h3>
            <div id="return-test-results"></div>
            <button onclick="testReturnManagement()">اختبار الرواجع</button>
        </div>

        <div class="test-section">
            <h3>💰 اختبار النظام المحاسبي</h3>
            <div id="accounting-test-results"></div>
            <button onclick="testAccountingSystem()">اختبار المحاسبة</button>
        </div>

        <div class="test-section">
            <h3>📊 اختبار نظام التقارير</h3>
            <div id="report-test-results"></div>
            <button onclick="testReportSystem()">اختبار التقارير</button>
        </div>

        <div class="test-section">
            <h3>🔧 إضافة بيانات تجريبية</h3>
            <div id="sample-data-results"></div>
            <button onclick="addSampleData()">إضافة بيانات تجريبية</button>
            <button onclick="clearSampleData()">مسح البيانات التجريبية</button>
        </div>

        <div class="test-section">
            <h3>🚀 اختبار شامل</h3>
            <div id="full-test-results"></div>
            <button onclick="runFullTest()">تشغيل اختبار شامل</button>
        </div>
    </div>

    <!-- تحميل ملفات النظام -->
    <script src="js/database.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/returns-manager.js"></script>
    <script src="js/accounting-manager.js"></script>
    <script src="js/reports-manager.js"></script>

    <script>
        // متغيرات الاختبار
        let testResults = {
            database: false,
            couriers: false,
            companies: false,
            orders: false,
            returns: false,
            accounting: false,
            reports: false
        };

        // تهيئة النظام
        async function initializeSystem() {
            try {
                await db.init();
                showResult('db-test-results', 'تم تهيئة قاعدة البيانات بنجاح', 'success');
                loadSystemStats();
            } catch (error) {
                showResult('db-test-results', 'خطأ في تهيئة قاعدة البيانات: ' + error.message, 'error');
            }
        }

        // عرض النتيجة
        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        // تحميل إحصائيات النظام
        async function loadSystemStats() {
            try {
                const orders = await db.getAll('orders');
                const couriers = await db.getAll('couriers');
                const companies = await db.getAll('companies');
                const returns = await db.getAll('returns');

                document.getElementById('total-orders').textContent = orders.length;
                document.getElementById('total-couriers').textContent = couriers.length;
                document.getElementById('total-companies').textContent = companies.length;
                document.getElementById('total-returns').textContent = returns.length;
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // اختبار قاعدة البيانات
        async function testDatabase() {
            const container = document.getElementById('db-test-results');
            container.innerHTML = '';

            try {
                // اختبار الإضافة
                const testData = { name: 'اختبار', createdDate: new Date() };
                const id = await db.add('regions', testData);
                showResult('db-test-results', 'تم اختبار الإضافة بنجاح - ID: ' + id, 'success');

                // اختبار القراءة
                const retrieved = await db.getById('regions', id);
                if (retrieved && retrieved.name === 'اختبار') {
                    showResult('db-test-results', 'تم اختبار القراءة بنجاح', 'success');
                } else {
                    showResult('db-test-results', 'فشل اختبار القراءة', 'error');
                }

                // اختبار التحديث
                await db.update('regions', { ...retrieved, name: 'اختبار محدث' });
                const updated = await db.getById('regions', id);
                if (updated.name === 'اختبار محدث') {
                    showResult('db-test-results', 'تم اختبار التحديث بنجاح', 'success');
                } else {
                    showResult('db-test-results', 'فشل اختبار التحديث', 'error');
                }

                // اختبار الحذف
                await db.delete('regions', id);
                const deleted = await db.getById('regions', id);
                if (!deleted) {
                    showResult('db-test-results', 'تم اختبار الحذف بنجاح', 'success');
                    testResults.database = true;
                } else {
                    showResult('db-test-results', 'فشل اختبار الحذف', 'error');
                }

            } catch (error) {
                showResult('db-test-results', 'خطأ في اختبار قاعدة البيانات: ' + error.message, 'error');
            }
        }

        // اختبار إدارة المندوبين
        async function testCourierManagement() {
            const container = document.getElementById('courier-test-results');
            container.innerHTML = '';

            try {
                const courierData = {
                    name: 'أحمد محمد',
                    phone: '0501234567',
                    region: 'الرياض',
                    commission: 5
                };

                const courierId = await dataManager.addCourier(courierData);
                showResult('courier-test-results', 'تم إضافة المندوب بنجاح - ID: ' + courierId, 'success');

                const courier = await db.getById('couriers', courierId);
                if (courier && courier.name === courierData.name) {
                    showResult('courier-test-results', 'تم التحقق من بيانات المندوب بنجاح', 'success');
                    testResults.couriers = true;
                } else {
                    showResult('courier-test-results', 'فشل في التحقق من بيانات المندوب', 'error');
                }

            } catch (error) {
                showResult('courier-test-results', 'خطأ في اختبار المندوبين: ' + error.message, 'error');
            }
        }

        // اختبار إدارة الشركات
        async function testCompanyManagement() {
            const container = document.getElementById('company-test-results');
            container.innerHTML = '';

            try {
                const companyData = {
                    name: 'شركة التجارة الإلكترونية',
                    phone: '0112345678',
                    email: '<EMAIL>',
                    address: 'الرياض، المملكة العربية السعودية'
                };

                const companyId = await dataManager.addCompany(companyData);
                showResult('company-test-results', 'تم إضافة الشركة بنجاح - ID: ' + companyId, 'success');

                const company = await db.getById('companies', companyId);
                if (company && company.name === companyData.name) {
                    showResult('company-test-results', 'تم التحقق من بيانات الشركة بنجاح', 'success');
                    testResults.companies = true;
                } else {
                    showResult('company-test-results', 'فشل في التحقق من بيانات الشركة', 'error');
                }

            } catch (error) {
                showResult('company-test-results', 'خطأ في اختبار الشركات: ' + error.message, 'error');
            }
        }

        // اختبار إدارة الطلبات
        async function testOrderManagement() {
            const container = document.getElementById('order-test-results');
            container.innerHTML = '';

            try {
                // الحصول على شركة ومندوب للاختبار
                const companies = await db.getAll('companies');
                const couriers = await db.getAll('couriers');

                if (companies.length === 0 || couriers.length === 0) {
                    showResult('order-test-results', 'يجب إضافة شركة ومندوب أولاً', 'info');
                    return;
                }

                const orderData = {
                    receiptNumber: utils.generateReceiptNumber(),
                    companyId: companies[0].id,
                    courierId: couriers[0].id,
                    customerName: 'سارة أحمد',
                    customerPhone: '0509876543',
                    address: 'الرياض، حي النرجس، شارع الأمير محمد بن عبدالعزيز',
                    amount: 150.50,
                    notes: 'طلب اختبار'
                };

                const orderId = await dataManager.addOrder(orderData);
                showResult('order-test-results', 'تم إضافة الطلب بنجاح - ID: ' + orderId, 'success');

                // اختبار تحديث حالة الطلب
                await dataManager.updateOrderStatus(orderId, 'delivered', 'تم التسليم بنجاح');
                showResult('order-test-results', 'تم تحديث حالة الطلب بنجاح', 'success');

                testResults.orders = true;

            } catch (error) {
                showResult('order-test-results', 'خطأ في اختبار الطلبات: ' + error.message, 'error');
            }
        }

        // اختبار إدارة الرواجع
        async function testReturnManagement() {
            const container = document.getElementById('return-test-results');
            container.innerHTML = '';

            try {
                const orders = await db.getAll('orders');
                const couriers = await db.getAll('couriers');

                if (orders.length === 0 || couriers.length === 0) {
                    showResult('return-test-results', 'يجب إضافة طلب ومندوب أولاً', 'info');
                    return;
                }

                const returnData = {
                    orderId: orders[0].id,
                    courierId: couriers[0].id,
                    returnType: 'full_return',
                    reason: 'الزبون غير موجود'
                };

                const returnId = await returnsManager.addReturn(returnData);
                showResult('return-test-results', 'تم إضافة الراجع بنجاح - ID: ' + returnId, 'success');

                // اختبار استلام الراجع
                await returnsManager.receiveReturn(returnId, 'موظف الاستلام', 'تم الاستلام');
                showResult('return-test-results', 'تم اختبار استلام الراجع بنجاح', 'success');

                testResults.returns = true;

            } catch (error) {
                showResult('return-test-results', 'خطأ في اختبار الرواجع: ' + error.message, 'error');
            }
        }

        // اختبار النظام المحاسبي
        async function testAccountingSystem() {
            const container = document.getElementById('accounting-test-results');
            container.innerHTML = '';

            try {
                const couriers = await db.getAll('couriers');
                const companies = await db.getAll('companies');

                if (couriers.length === 0 || companies.length === 0) {
                    showResult('accounting-test-results', 'يجب إضافة مندوب وشركة أولاً', 'info');
                    return;
                }

                // اختبار حساب عمولات المندوب
                const commissions = await accountingManager.calculateCourierCommissions(couriers[0].id);
                showResult('accounting-test-results', `إجمالي عمولة المندوب: ${utils.formatCurrency(commissions.totalCommission)}`, 'success');

                // اختبار حساب أرباح الشركة
                const profits = await accountingManager.calculateCompanyProfits(companies[0].id);
                showResult('accounting-test-results', `إجمالي ربح الشركة: ${utils.formatCurrency(profits.totalProfit)}`, 'success');

                testResults.accounting = true;

            } catch (error) {
                showResult('accounting-test-results', 'خطأ في اختبار المحاسبة: ' + error.message, 'error');
            }
        }

        // اختبار نظام التقارير
        async function testReportSystem() {
            const container = document.getElementById('report-test-results');
            container.innerHTML = '';

            try {
                // اختبار التقرير اليومي
                const dailyReport = await reportsManager.generateDailySummaryReport();
                showResult('report-test-results', 'تم إنشاء التقرير اليومي بنجاح', 'success');

                // اختبار تقرير أداء المندوبين
                const courierReport = await reportsManager.generateCourierPerformanceReport();
                showResult('report-test-results', `تقرير المندوبين: ${courierReport.totalCouriers} مندوب`, 'success');

                testResults.reports = true;

            } catch (error) {
                showResult('report-test-results', 'خطأ في اختبار التقارير: ' + error.message, 'error');
            }
        }

        // إضافة بيانات تجريبية
        async function addSampleData() {
            const container = document.getElementById('sample-data-results');
            container.innerHTML = '';

            try {
                // إضافة مندوبين تجريبيين
                const sampleCouriers = [
                    { name: 'محمد أحمد', phone: '0501111111', region: 'الرياض', commission: 5 },
                    { name: 'علي سعد', phone: '0502222222', region: 'جدة', commission: 4.5 },
                    { name: 'خالد محمد', phone: '0503333333', region: 'الدمام', commission: 5.5 }
                ];

                for (const courier of sampleCouriers) {
                    await dataManager.addCourier(courier);
                }
                showResult('sample-data-results', 'تم إضافة المندوبين التجريبيين', 'success');

                // إضافة شركات تجريبية
                const sampleCompanies = [
                    { name: 'متجر الإلكترونيات', phone: '0111111111', email: '<EMAIL>' },
                    { name: 'متجر الأزياء', phone: '0112222222', email: '<EMAIL>' },
                    { name: 'متجر الكتب', phone: '0113333333', email: '<EMAIL>' }
                ];

                for (const company of sampleCompanies) {
                    await dataManager.addCompany(company);
                }
                showResult('sample-data-results', 'تم إضافة الشركات التجريبية', 'success');

                loadSystemStats();

            } catch (error) {
                showResult('sample-data-results', 'خطأ في إضافة البيانات التجريبية: ' + error.message, 'error');
            }
        }

        // مسح البيانات التجريبية
        async function clearSampleData() {
            const container = document.getElementById('sample-data-results');
            container.innerHTML = '';

            try {
                const tables = ['orders', 'returns', 'couriers', 'companies', 'accounting', 'order_history'];
                
                for (const table of tables) {
                    const records = await db.getAll(table);
                    for (const record of records) {
                        await db.delete(table, record.id);
                    }
                }

                showResult('sample-data-results', 'تم مسح جميع البيانات التجريبية', 'success');
                loadSystemStats();

            } catch (error) {
                showResult('sample-data-results', 'خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }

        // مسح قاعدة البيانات
        async function clearDatabase() {
            if (confirm('هل أنت متأكد من مسح قاعدة البيانات بالكامل؟')) {
                try {
                    db.close();
                    indexedDB.deleteDatabase('DeliverySystemDB');
                    location.reload();
                } catch (error) {
                    showResult('db-test-results', 'خطأ في مسح قاعدة البيانات: ' + error.message, 'error');
                }
            }
        }

        // تشغيل اختبار شامل
        async function runFullTest() {
            const container = document.getElementById('full-test-results');
            container.innerHTML = '';

            showResult('full-test-results', 'بدء الاختبار الشامل...', 'info');

            // تشغيل جميع الاختبارات
            await testDatabase();
            await testCourierManagement();
            await testCompanyManagement();
            await testOrderManagement();
            await testReturnManagement();
            await testAccountingSystem();
            await testReportSystem();

            // عرض النتيجة النهائية
            const passedTests = Object.values(testResults).filter(result => result).length;
            const totalTests = Object.keys(testResults).length;

            if (passedTests === totalTests) {
                showResult('full-test-results', `✅ نجح جميع الاختبارات (${passedTests}/${totalTests})`, 'success');
            } else {
                showResult('full-test-results', `⚠️ نجح ${passedTests} من ${totalTests} اختبارات`, 'error');
            }

            loadSystemStats();
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            initializeSystem();
        });
    </script>
</body>
</html>
