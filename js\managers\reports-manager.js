/**
 * مدير التقارير
 * Reports Manager
 */

class ReportsManager {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
    }

    /**
     * إنشاء التقرير اليومي
     * Generate daily summary report
     */
    async generateDailySummaryReport(date = new Date()) {
        try {
            const dailyData = await accountingManager.generateDailyStatement(date);

            return {
                type: 'daily_summary',
                title: 'التقرير اليومي',
                date: date,
                generatedAt: new Date(),
                summary: dailyData.summary,
                courierStats: dailyData.courierStats
            };

        } catch (error) {
            console.error('خطأ في إنشاء التقرير اليومي:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير أداء المندوبين
     * Generate courier performance report
     */
    async generateCourierPerformanceReport(dateFrom, dateTo) {
        try {
            const orders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                return orderDate >= dateFrom && orderDate <= dateTo && !order.isArchived;
            });

            const couriers = await db.getAll('couriers');
            const courierStats = [];

            for (const courier of couriers) {
                const courierOrders = orders.filter(o => o.courierId === courier.id);
                
                if (courierOrders.length > 0) {
                    const totalOrders = courierOrders.length;
                    const deliveredOrders = courierOrders.filter(o => o.status === 'delivered').length;
                    const returnedOrders = courierOrders.filter(o => 
                        o.status === 'returned' || o.status === 'partial_return'
                    ).length;

                    const successRate = totalOrders > 0 ? (deliveredOrders / totalOrders * 100) : 0;
                    const returnRate = totalOrders > 0 ? (returnedOrders / totalOrders * 100) : 0;

                    const totalAmount = courierOrders
                        .filter(o => o.status === 'delivered')
                        .reduce((sum, o) => sum + (o.amount || 0), 0);
                    const totalCommission = totalAmount * (courier.commission || 0) / 100;

                    courierStats.push({
                        courier,
                        totalOrders,
                        deliveredOrders,
                        returnedOrders,
                        successRate,
                        returnRate,
                        totalCommission
                    });
                }
            }

            // ترتيب حسب معدل النجاح
            courierStats.sort((a, b) => b.successRate - a.successRate);

            const averageSuccessRate = courierStats.length > 0 ? 
                courierStats.reduce((sum, c) => sum + c.successRate, 0) / courierStats.length : 0;

            return {
                type: 'courier_performance',
                title: 'تقرير أداء المندوبين',
                dateFrom,
                dateTo,
                generatedAt: new Date(),
                totalCouriers: courierStats.length,
                averageSuccessRate,
                couriers: courierStats
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير أداء المندوبين:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير تحليل الشركات
     * Generate company analysis report
     */
    async generateCompanyAnalysisReport(dateFrom, dateTo) {
        try {
            const companiesData = await accountingManager.generateCompaniesProfitReport(dateFrom, dateTo);

            // إضافة معدل رضا العملاء (افتراضي)
            const companiesWithSatisfaction = companiesData.companies.map(company => ({
                ...company,
                customerSatisfaction: Math.random() * 20 + 80 // رقم عشوائي بين 80-100
            }));

            return {
                type: 'company_analysis',
                title: 'تقرير تحليل الشركات',
                dateFrom,
                dateTo,
                generatedAt: new Date(),
                totalCompanies: companiesWithSatisfaction.length,
                totalRevenue: companiesData.totalRevenue,
                totalProfit: companiesData.totalProfit,
                companies: companiesWithSatisfaction
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير تحليل الشركات:', error);
            throw error;
        }
    }

    /**
     * إنشاء الملخص المالي
     * Generate financial summary report
     */
    async generateFinancialSummaryReport(dateFrom, dateTo) {
        try {
            const totalProfits = await accountingManager.calculateTotalProfits(dateFrom, dateTo);
            const orders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                return orderDate >= dateFrom && orderDate <= dateTo && !order.isArchived;
            });

            const deliveredOrders = orders.filter(o => o.status === 'delivered');
            const returnedOrders = orders.filter(o => 
                o.status === 'returned' || o.status === 'partial_return'
            );

            const averageOrderValue = deliveredOrders.length > 0 ? 
                totalProfits.totalRevenue / deliveredOrders.length : 0;

            const returnRate = orders.length > 0 ? (returnedOrders.length / orders.length * 100) : 0;
            const costRatio = totalProfits.totalRevenue > 0 ? 
                (totalProfits.totalCommissions / totalProfits.totalRevenue * 100) : 0;

            const activeCouriers = (await db.getAll('couriers', c => c.isActive)).length;
            const activeCompanies = (await db.getAll('companies', c => c.isActive)).length;

            return {
                type: 'financial_summary',
                title: 'الملخص المالي',
                dateFrom,
                dateTo,
                generatedAt: new Date(),
                revenue: {
                    totalRevenue: totalProfits.totalRevenue,
                    averageOrderValue
                },
                costs: {
                    totalCommissions: totalProfits.totalCommissions,
                    operatingCosts: 0, // يمكن إضافة التكاليف التشغيلية لاحقاً
                    costRatio
                },
                profit: {
                    grossProfit: totalProfits.totalProfit,
                    netProfit: totalProfits.totalProfit, // نفس الربح الإجمالي حالياً
                    profitMargin: totalProfits.profitMargin
                },
                performance: {
                    totalOrders: orders.length,
                    totalReturns: returnedOrders.length,
                    returnRate,
                    activeCouriers,
                    activeCompanies
                }
            };

        } catch (error) {
            console.error('خطأ في إنشاء الملخص المالي:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير تحليل الرواجع
     * Generate returns analysis report
     */
    async generateReturnsAnalysisReport(dateFrom, dateTo) {
        try {
            const returns = await db.getAll('returns', returnItem => {
                const returnDate = new Date(returnItem.createdDate);
                return returnDate >= dateFrom && returnDate <= dateTo;
            });

            const totalReturns = returns.length;
            const receivedReturns = returns.filter(r => r.status === 'received').length;
            const pendingReturns = returns.filter(r => r.status === 'pending').length;

            // تجميع الرواجع حسب النوع
            const returnsByType = {};
            returns.forEach(returnItem => {
                const type = returnItem.type || 'غير محدد';
                returnsByType[type] = (returnsByType[type] || 0) + 1;
            });

            // تحليل الرواجع حسب المندوب
            const couriers = await db.getAll('couriers');
            const courierAnalysis = [];

            for (const courier of couriers) {
                const courierReturns = returns.filter(r => r.courierId === courier.id);
                
                if (courierReturns.length > 0) {
                    const totalAmount = courierReturns.reduce((sum, r) => sum + (r.amount || 0), 0);
                    const returnsByType = {};
                    
                    courierReturns.forEach(returnItem => {
                        const type = returnItem.type || 'غير محدد';
                        returnsByType[type] = (returnsByType[type] || 0) + 1;
                    });

                    courierAnalysis.push({
                        courier,
                        totalReturns: courierReturns.length,
                        totalAmount,
                        returnsByType
                    });
                }
            }

            return {
                type: 'returns_analysis',
                title: 'تقرير تحليل الرواجع',
                dateFrom,
                dateTo,
                generatedAt: new Date(),
                summary: {
                    totalReturns,
                    receivedReturns,
                    pendingReturns,
                    returnsByType
                },
                courierAnalysis
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير تحليل الرواجع:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير الطلبات المفصل
     * Generate detailed orders report
     */
    async generateDetailedOrdersReport(filters = {}) {
        try {
            let orders = await db.getAll('orders', order => !order.isArchived);

            // تطبيق الفلاتر
            if (filters.dateFrom && filters.dateTo) {
                orders = orders.filter(order => {
                    const orderDate = new Date(order.createdDate);
                    return orderDate >= filters.dateFrom && orderDate <= filters.dateTo;
                });
            }

            if (filters.companyId) {
                orders = orders.filter(order => order.companyId === filters.companyId);
            }

            if (filters.courierId) {
                orders = orders.filter(order => order.courierId === filters.courierId);
            }

            // إحصائيات الحالات
            const statusCounts = {};
            orders.forEach(order => {
                const status = order.status || 'غير محدد';
                statusCounts[status] = (statusCounts[status] || 0) + 1;
            });

            const totalAmount = orders.reduce((sum, order) => sum + (order.amount || 0), 0);

            // تحضير بيانات الطلبات للعرض
            const companies = await db.getAll('companies');
            const couriers = await db.getAll('couriers');

            const formattedOrders = orders.map(order => {
                const company = companies.find(c => c.id === order.companyId);
                const courier = couriers.find(c => c.id === order.courierId);

                return {
                    ...order,
                    companyName: company ? company.name : 'غير محدد',
                    courierName: courier ? courier.name : 'غير مُسند',
                    formattedAmount: utils.formatCurrency(order.amount || 0),
                    formattedDate: utils.formatDate(order.createdDate, 'DD/MM/YYYY'),
                    statusText: this.getStatusText(order.status)
                };
            });

            return {
                type: 'orders_detailed',
                title: 'تقرير الطلبات المفصل',
                dateFrom: filters.dateFrom,
                dateTo: filters.dateTo,
                generatedAt: new Date(),
                summary: {
                    totalOrders: orders.length,
                    totalAmount,
                    statusCounts
                },
                orders: formattedOrders
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير الطلبات المفصل:', error);
            throw error;
        }
    }

    /**
     * الحصول على نص الحالة
     * Get status text
     */
    getStatusText(status) {
        const statusTexts = {
            'delivered': 'مُسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي',
            'delayed': 'مؤجل'
        };
        return statusTexts[status] || 'غير محدد';
    }

    /**
     * تصدير التقرير إلى Excel
     * Export report to Excel
     */
    exportToExcel(reportData) {
        try {
            // تحويل بيانات التقرير إلى CSV
            let csvContent = '';
            
            // إضافة معلومات التقرير
            csvContent += `${reportData.title}\n`;
            csvContent += `تاريخ الإنشاء: ${utils.formatDate(reportData.generatedAt, 'DD/MM/YYYY HH:mm')}\n`;
            
            if (reportData.dateFrom && reportData.dateTo) {
                csvContent += `الفترة: ${utils.formatDate(reportData.dateFrom, 'DD/MM/YYYY')} - ${utils.formatDate(reportData.dateTo, 'DD/MM/YYYY')}\n`;
            }
            
            csvContent += '\n';

            // إضافة البيانات حسب نوع التقرير
            if (reportData.type === 'orders_detailed' && reportData.orders) {
                csvContent += 'رقم الوصل,الشركة,اسم الزبون,المبلغ,المندوب,الحالة,التاريخ\n';
                reportData.orders.forEach(order => {
                    csvContent += `${order.receiptNumber},${order.companyName},${order.customerName},${order.amount},${order.courierName},${order.statusText},${order.formattedDate}\n`;
                });
            }

            // تصدير الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${reportData.type}_${utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`;
            link.click();

        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            throw error;
        }
    }

    /**
     * طباعة التقرير
     * Print report
     */
    printReport(reportData) {
        try {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>${reportData.title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .header { text-align: center; margin-bottom: 20px; }
                        .content { margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${reportData.title}</h1>
                        <p>تاريخ الإنشاء: ${utils.formatDate(reportData.generatedAt, 'DD/MM/YYYY HH:mm')}</p>
                    </div>
                    <div class="content">
                        <!-- محتوى التقرير سيتم إضافته هنا -->
                        <p>التقرير جاهز للطباعة</p>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();

        } catch (error) {
            console.error('خطأ في طباعة التقرير:', error);
            throw error;
        }
    }

    /**
     * مسح الذاكرة المؤقتة
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }
}

// إنشاء مثيل مدير التقارير
const reportsManager = new ReportsManager();
window.reportsManager = reportsManager;
