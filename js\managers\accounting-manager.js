/**
 * مدير المحاسبة
 * Accounting Manager
 */

class AccountingManager {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
    }

    /**
     * حساب إجمالي الأرباح
     * Calculate total profits
     */
    async calculateTotalProfits(dateFrom, dateTo) {
        try {
            const cacheKey = `total_profits_${dateFrom.getTime()}_${dateTo.getTime()}`;
            
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return cached.data;
                }
            }

            // الحصول على الطلبات في الفترة المحددة
            const orders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                return orderDate >= dateFrom && orderDate <= dateTo && !order.isArchived;
            });

            // حساب الإحصائيات
            const totalOrders = orders.length;
            const deliveredOrders = orders.filter(o => o.status === 'delivered');
            const totalRevenue = deliveredOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
            
            // حساب العمولات
            const couriers = await db.getAll('couriers');
            let totalCommissions = 0;
            
            for (const order of deliveredOrders) {
                if (order.courierId) {
                    const courier = couriers.find(c => c.id === order.courierId);
                    if (courier) {
                        const commission = (order.amount || 0) * (courier.commission || 0) / 100;
                        totalCommissions += commission;
                    }
                }
            }

            const totalProfit = totalRevenue - totalCommissions;
            const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0;

            const result = {
                totalOrders,
                deliveredOrders: deliveredOrders.length,
                totalRevenue,
                totalCommissions,
                totalProfit,
                profitMargin
            };

            // حفظ في الذاكرة المؤقتة
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });

            return result;

        } catch (error) {
            console.error('خطأ في حساب إجمالي الأرباح:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير أرباح المندوبين
     * Generate couriers profit report
     */
    async generateCouriersProfitReport(dateFrom, dateTo) {
        try {
            const orders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                return orderDate >= dateFrom && orderDate <= dateTo && 
                       order.status === 'delivered' && !order.isArchived;
            });

            const couriers = await db.getAll('couriers');
            const courierStats = [];

            for (const courier of couriers) {
                const courierOrders = orders.filter(o => o.courierId === courier.id);
                
                if (courierOrders.length > 0) {
                    const totalOrders = courierOrders.length;
                    const totalAmount = courierOrders.reduce((sum, o) => sum + (o.amount || 0), 0);
                    const totalCommission = totalAmount * (courier.commission || 0) / 100;
                    const averageCommission = totalOrders > 0 ? totalCommission / totalOrders : 0;

                    courierStats.push({
                        courier,
                        totalOrders,
                        totalAmount,
                        commissionRate: courier.commission || 0,
                        totalCommission,
                        averageCommission
                    });
                }
            }

            // ترتيب حسب إجمالي العمولة
            courierStats.sort((a, b) => b.totalCommission - a.totalCommission);

            return {
                couriers: courierStats,
                totalCommissions: courierStats.reduce((sum, c) => sum + c.totalCommission, 0)
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير أرباح المندوبين:', error);
            throw error;
        }
    }

    /**
     * إنشاء تقرير أرباح الشركات
     * Generate companies profit report
     */
    async generateCompaniesProfitReport(dateFrom, dateTo) {
        try {
            const orders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                return orderDate >= dateFrom && orderDate <= dateTo && 
                       order.status === 'delivered' && !order.isArchived;
            });

            const companies = await db.getAll('companies');
            const companyStats = [];

            for (const company of companies) {
                const companyOrders = orders.filter(o => o.companyId === company.id);
                
                if (companyOrders.length > 0) {
                    const totalOrders = companyOrders.length;
                    const totalRevenue = companyOrders.reduce((sum, o) => sum + (o.amount || 0), 0);
                    
                    // حساب العمولات المدفوعة للمندوبين
                    const couriers = await db.getAll('couriers');
                    let totalCommissions = 0;
                    
                    for (const order of companyOrders) {
                        if (order.courierId) {
                            const courier = couriers.find(c => c.id === order.courierId);
                            if (courier) {
                                totalCommissions += (order.amount || 0) * (courier.commission || 0) / 100;
                            }
                        }
                    }

                    const totalProfit = totalRevenue - totalCommissions;
                    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0;
                    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

                    // حساب معدل الرواجع
                    const allCompanyOrders = await db.getAll('orders', o => o.companyId === company.id);
                    const returnedOrders = allCompanyOrders.filter(o => 
                        o.status === 'returned' || o.status === 'partial_return'
                    );
                    const returnRate = allCompanyOrders.length > 0 ? 
                        (returnedOrders.length / allCompanyOrders.length * 100) : 0;

                    companyStats.push({
                        company,
                        totalOrders,
                        totalRevenue,
                        totalCommissions,
                        totalProfit,
                        profitMargin,
                        averageOrderValue,
                        returnRate
                    });
                }
            }

            // ترتيب حسب إجمالي الإيرادات
            companyStats.sort((a, b) => b.totalRevenue - a.totalRevenue);

            return {
                companies: companyStats,
                totalRevenue: companyStats.reduce((sum, c) => sum + c.totalRevenue, 0),
                totalProfit: companyStats.reduce((sum, c) => sum + c.totalProfit, 0)
            };

        } catch (error) {
            console.error('خطأ في إنشاء تقرير أرباح الشركات:', error);
            throw error;
        }
    }

    /**
     * إنشاء الكشف اليومي
     * Generate daily statement
     */
    async generateDailyStatement(date) {
        try {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            // الحصول على طلبات اليوم
            const orders = await db.getAll('orders', order => {
                const orderDate = new Date(order.createdDate);
                return orderDate >= startOfDay && orderDate <= endOfDay && !order.isArchived;
            });

            const deliveredOrders = orders.filter(o => o.status === 'delivered');
            const totalRevenue = deliveredOrders.reduce((sum, o) => sum + (o.amount || 0), 0);

            // حساب العمولات
            const couriers = await db.getAll('couriers');
            let totalCommissions = 0;
            const courierStats = [];

            for (const courier of couriers) {
                const courierOrders = deliveredOrders.filter(o => o.courierId === courier.id);
                
                if (courierOrders.length > 0) {
                    const revenue = courierOrders.reduce((sum, o) => sum + (o.amount || 0), 0);
                    const commission = revenue * (courier.commission || 0) / 100;
                    totalCommissions += commission;

                    courierStats.push({
                        courier,
                        orders: courierOrders.length,
                        revenue,
                        commission
                    });
                }
            }

            const totalProfit = totalRevenue - totalCommissions;

            return {
                date,
                summary: {
                    totalOrders: orders.length,
                    deliveredOrders: deliveredOrders.length,
                    totalRevenue,
                    totalCommissions,
                    totalProfit
                },
                courierStats
            };

        } catch (error) {
            console.error('خطأ في إنشاء الكشف اليومي:', error);
            throw error;
        }
    }

    /**
     * الحصول على المعاملات
     * Get transactions
     */
    async getTransactions(filters = {}) {
        try {
            // في الوقت الحالي، نرجع مصفوفة فارغة
            // يمكن تطوير هذا لاحقاً لإدارة المعاملات المالية
            return [];

        } catch (error) {
            console.error('خطأ في الحصول على المعاملات:', error);
            throw error;
        }
    }

    /**
     * مسح الذاكرة المؤقتة
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }
}

// إنشاء مثيل مدير المحاسبة
const accountingManager = new AccountingManager();
window.accountingManager = accountingManager;
